// objective.ts
// Centralized objectives (and tips) for lessons.
// Pure data module: safe to import server- or client-side.

// ---------- Types ----------
export type IconKey = "target" | "check" | "none";

export interface ObjectiveBullet {
  text: string;
  icon?: IconKey; // consumer decides how to render the icon
}

export interface LessonObjectives {
  lesson: number;
  heading: string; // e.g., "Student checklist:"
  soFar: ObjectiveBullet[]; // kept for compatibility; not used in UI
  endGoals: ObjectiveBullet[]; // "By the end of this level, I should be able to:"
  tips: string[]; // Tips shown under the objectives
}

/**
 * Optional hint map so your UI knows which icon component to use.
 * Example (React):
 *   import { IconTarget, IconCircleCheck } from "@tabler/icons-react";
 *   const Icons = { target: IconTarget, check: IconCircleCheck };
 */
export const IconHints: Record<IconKey, { library: string; name: string }> = {
  target: { library: "@tabler/icons-react", name: "IconTarget" },
  check: { library: "@tabler/icons-react", name: "IconCircleCheck" },
  none: { library: "", name: "" },
};

// ---------- Lesson 1: Individual Alphabet Letters ----------
const LESSON_1_OBJECTIVES: LessonObjectives = {
  lesson: 1,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "recognise and say the name of every letter of the alphabet.",
      icon: "target",
    },
    {
      text: "tell the difference between letters that look similar by their shape and dots.",
      icon: "target",
    },
    { text: "say each letter clearly and correctly.", icon: "target" },
  ],
  tips: [
    "Practice makes perfect! Say the letters out loud every day.",
    "If you're unsure about a sound, click on the card to hear it pronounced correctly.",
    "Use your finger to trace the letters to help you remember their shapes.",
  ],
};

// ---------- Lesson 2: Combined Alphabet Letters ----------
const LESSON_2_OBJECTIVES: LessonObjectives = {
  lesson: 2,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "recognise how letters look when they are joined together.",
      icon: "target",
    },
    {
      text: "identify individual letters within a combined form.",
      icon: "target",
    },
    {
      text: "understand that a letter's shape can change when it's at the start, middle, or end of a group.",
      icon: "target",
    },
  ],
  tips: [
    "Look closely at the dots and main parts of each letter to identify it.",
    "Don't guess! If you get stuck, click on the card to hear the sounds and see the individual letters.",
    "Practice reading the combined letters from right to left smoothly.",
  ],
};

// ---------- Lesson 3: Disconnected Letters (Huroof e Muqatta'at) ----------
const LESSON_3_OBJECTIVES: LessonObjectives = {
  lesson: 3,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "recognise these special letter combinations that appear at the start of some chapters in the Qur'an.",
      icon: "target",
    },
    {
      text: "understand that these letters are read by their individual names, not their sounds.",
      icon: "target",
    },
    {
      text: "read each letter in the sequence clearly and correctly.",
      icon: "target",
    },
  ],
  tips: [
    "This is a special lesson! These letters are a mystery from Allah.",
    "Remember to say the full name of the letter (e.g., 'Alif', 'Laam', 'Meem').",
    "If you forget a letter's name, click on the card to listen to the audio.",
  ],
};

// ---------- Lesson 4: Letters with Vowel Marks (Harakat) ----------
const LESSON_4_OBJECTIVES: LessonObjectives = {
  lesson: 4,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "know the names and signs for Fatha, Kasra, and Damma.",
      icon: "target",
    },
    {
      text: "say the correct short sounds: 'a' (Fatha), 'i' (Kasra), and 'u' (Damma).",
      icon: "target",
    },
    {
      text: "read letters with Harakat quickly without stretching the sound.",
      icon: "target",
    },
  ],
  tips: [
    "Fatha (above) opens your mouth, Kasra (below) makes you smile, Damma (curl above) rounds your lips.",
    "Keep the sounds short and sharp, like one quick beat.",
    "If you're unsure which sound to make, click the card to hear the correct pronunciation.",
  ],
};

// ---------- Lesson 5: Letters with Tanween ----------
const LESSON_5_OBJECTIVES: LessonObjectives = {
  lesson: 5,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "recognise the signs for double Fatha (Fathatain), double Kasra (Kasratain), and double Damma (Dammatain).",
      icon: "target",
    },
    {
      text: "understand that Tanween adds a short 'n' sound at the end.",
      icon: "target",
    },
    {
      text: "correctly say 'an', 'in', and 'un' for each type of Tanween.",
      icon: "target",
    },
  ],
  tips: [
    "Think of Tanween as a vowel mark with a hidden 'Noon' sound.",
    "The 'n' sound is quick and clear.",
    "Listen carefully by clicking the cards to hear the difference between a normal vowel and Tanween.",
  ],
};

// ---------- Lesson 6: Exercises on Vowel Marks and Tanween ----------
const LESSON_6_OBJECTIVES: LessonObjectives = {
  lesson: 6,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    { text: "read words that mix both Harakat and Tanween.", icon: "target" },
    {
      text: "switch smoothly between single vowel sounds ('a', 'i', 'u') and Tanween sounds ('an', 'in', 'un').",
      icon: "target",
    },
    {
      text: "build my confidence by practicing everything from the last two lessons.",
      icon: "target",
    },
  ],
  tips: [
    "Take your time and look at each mark carefully before you read.",
    "If a word seems tricky, break it down and read it letter by letter.",
    "Feeling stuck? Click the card to hear the whole word pronounced correctly.",
  ],
};

// ---------- Lesson 7: Small Alif, Small Ya, and Small Waw ----------
const LESSON_7_OBJECTIVES: LessonObjectives = {
  lesson: 7,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "recognise the Standing Fatha, Standing Kasra, and Standing Damma.",
      icon: "target",
    },
    {
      text: "know that these signs mean I should stretch the sound for two beats.",
      icon: "target",
    },
    {
      text: "pronounce the long 'aa', 'ee', and 'oo' sounds correctly.",
      icon: "target",
    },
  ],
  tips: [
    "These are like long vowels. Hold the sound for about two counts.",
    "Standing Fatha sounds like Fatha + Alif.",
    "Need help with the timing? Click the card and listen to how long the sound is held.",
  ],
};

// ---------- Lesson 8: Letters of Elongation (Madd) and Softening (Leen) ----------
const LESSON_8_OBJECTIVES: LessonObjectives = {
  lesson: 8,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "identify the three Madd letters: Alif, Waw, and Yaa.",
      icon: "target",
    },
    {
      text: "know when they stretch a sound for two beats (e.g., Fatha before Alif).",
      icon: "target",
    },
    {
      text: "identify the two Leen letters: Waw Sakin and Yaa Sakin after a Fatha.",
      icon: "target",
    },
    {
      text: "pronounce the soft 'aw' and 'ay' sounds of Leen letters.",
      icon: "target",
    },
  ],
  tips: [
    "Madd means to stretch. Leen means soft and easy.",
    "Pay close attention to the vowel *before* the Waw or Yaa to know if it is Madd or Leen.",
    "The sounds are different! Click the cards to hear examples of Madd ('oo') and Leen ('aw').",
  ],
};

// ---------- Lesson 9: Exercises on Madd Leen ----------
const LESSON_9_OBJECTIVES: LessonObjectives = {
  lesson: 9,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "read words and phrases that contain both Madd and Leen letters.",
      icon: "target",
    },
    {
      text: "differentiate between a short vowel, a long Madd vowel, and a soft Leen sound.",
      icon: "target",
    },
    {
      text: "improve my reading flow when I see these letters.",
      icon: "target",
    },
  ],
  tips: [
    "This lesson puts everything together. Go slowly at first.",
    "Remember: Madd is a stretch, Leen is a glide.",
    "Keep practicing! If you're unsure, just click the card to hear the audio.",
  ],
};

// ---------- Lesson 10: Exercises on Tanween and Madd Letters ----------
const LESSON_10_OBJECTIVES: LessonObjectives = {
  lesson: 10,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "read words that combine Tanween with Madd and Leen letters.",
      icon: "target",
    },
    {
      text: "apply all the vowel and stretching rules I have learned so far in longer words.",
      icon: "target",
    },
    {
      text: "become more confident in reading different combinations of letters and signs.",
      icon: "target",
    },
  ],
  tips: [
    "This is a great review of many rules.",
    "Sound out each part of the word if you need to, then say it all together.",
    "Don't give up! If a word is hard, use the audio help by clicking the card.",
  ],
};

// ---------- Lesson 11: Sukoon Introduction ----------
const LESSON_11_OBJECTIVES: LessonObjectives = {
  lesson: 11,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "recognise the Sukoon sign and know it means the letter has no vowel.",
      icon: "target",
    },
    {
      text: "join a letter with Sukoon to the letter before it to make one sound.",
      icon: "target",
    },
    {
      text: "learn that the sound stops on a letter with Sukoon.",
      icon: "target",
    },
  ],
  tips: [
    "A letter with Sukoon is 'resting'. It can't be said alone.",
    "Always connect the Sukoon to the letter with a vowel before it.",
    "Click the cards to hear how the sounds merge together.",
  ],
};

// ---------- Lesson 12: Sukoon Exercises ----------
const LESSON_12_OBJECTIVES: LessonObjectives = {
  lesson: 12,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "practice reading many different words with Sukoon.",
      icon: "target",
    },
    {
      text: "learn about the five 'bouncing' letters (Qalqalah): ق, ط, ب, ج, د.",
      icon: "target",
    },
    {
      text: "make a light echo sound when one of the Qalqalah letters has a Sukoon.",
      icon: "target",
    },
  ],
  tips: [
    "For most letters, the Sukoon sound is sharp and stops. For Qalqalah letters, it echoes!",
    "Listen carefully to the audio on the cards to hear the special 'bounce' sound.",
    "Reading out loud helps you check if you are making the echo correctly.",
  ],
};

// ---------- Lesson 13: Shaddah Introduction ----------
const LESSON_13_OBJECTIVES: LessonObjectives = {
  lesson: 13,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "recognise the Shaddah sign (looks like a small 'w') and know it means 'double letter'.",
      icon: "target",
    },
    {
      text: "understand a Shaddah means I read the letter twice: once with Sukoon, and once with its vowel.",
      icon: "target",
    },
    {
      text: "press or stress the letter with Shaddah when I read it.",
      icon: "target",
    },
  ],
  tips: [
    "Think of Shaddah as a shortcut for writing the same letter twice in a row.",
    "Example: 'ab-ba'. The Shaddah joins them together.",
    "Click on the cards to hear how the voice presses on the letter with Shaddah.",
  ],
};

// ---------- Lesson 14: Shaddah Exercises ----------
const LESSON_14_OBJECTIVES: LessonObjectives = {
  lesson: 14,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "master reading words and short sentences with Shaddah.",
      icon: "target",
    },
    {
      text: "learn to make a nasal sound (Ghunnah) when I see a Shaddah on Noon (نّ) or Meem (مّ).",
      icon: "target",
    },
    {
      text: "hold the Ghunnah sound for a moment before continuing.",
      icon: "target",
    },
  ],
  tips: [
    "Ghunnah is a buzzing sound you make in your nose.",
    "Practice making the 'nnn' and 'mmm' sound with a Shaddah.",
    "Listen very carefully to the audio for Noon and Meem with Shaddah to get the sound right!",
  ],
};

// ---------- Lesson 15: Combined Shaddah and Sukoon ----------
const LESSON_15_OBJECTIVES: LessonObjectives = {
  lesson: 15,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "read more complex words that have both a Shaddah and a Sukoon in them.",
      icon: "target",
    },
    {
      text: "apply the rule of pressing on the Shaddah and then stopping on the Sukoon.",
      icon: "target",
    },
    {
      text: "read smoothly without getting confused by multiple signs.",
      icon: "target",
    },
  ],
  tips: [
    "These words can look tricky, but you have all the skills you need!",
    "Break the word into syllables to make it easier.",
    "If you're stuck, clicking the card will show you how to read it correctly step-by-step.",
  ],
};

// ---------- Lesson 16: Double Shaddah Exercises ----------
const LESSON_16_OBJECTIVES: LessonObjectives = {
  lesson: 16,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "read words that contain two different letters with Shaddah.",
      icon: "target",
    },
    {
      text: "pronounce each doubled letter clearly with the correct pressure.",
      icon: "target",
    },
    {
      text: "build my fluency and confidence with very advanced words.",
      icon: "target",
    },
  ],
  tips: [
    "Remember to apply pressure on the first Shaddah, then continue reading until you apply pressure on the second one.",
    "Reading out loud is very helpful here to make sure you're not skipping any sounds.",
    "These are some of the hardest words in the Qaida! Don't worry if you need to use the audio help.",
  ],
};

// ---------- Lesson 17: Madd letters followed by a Shaddah ----------
const LESSON_17_OBJECTIVES: LessonObjectives = {
  lesson: 17,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "recognise when a Madd letter (Alif, Waw, or Yaa) is followed by a letter with a Shaddah.",
      icon: "target",
    },
    {
      text: "understand that this is a special case that requires the longest stretch.",
      icon: "target",
    },
    {
      text: "practice stretching the sound for up to 6 beats before saying the Shaddah letter.",
      icon: "target",
    },
  ],
  tips: [
    "This is the most powerful Madd! You need to hold the sound for a long time.",
    "Try counting to 6 in your head as you stretch the vowel.",
    "Listen to the examples on the cards very carefully to master the timing of this long stretch.",
  ],
};

// ---------- Lesson 18: Final Review Exercises ----------
const LESSON_18_OBJECTIVES: LessonObjectives = {
  lesson: 18,
  heading: "Student checklist:",
  soFar: [],
  endGoals: [
    {
      text: "read full sentences and short verses by combining all the rules I have learned.",
      icon: "target",
    },
    {
      text: "learn the basic rules of how to stop correctly at the end of a word (Waqf).",
      icon: "target",
    },
    {
      text: "feel confident that I can now begin reading the Qur'an.",
      icon: "target",
    },
  ],
  tips: [
    "Congratulations on reaching the final lesson!",
    "Remember, when you stop on a word, you usually change the last vowel to a Sukoon.",
    "Review any lesson you feel unsure about. The goal is to be confident in every rule.",
    "Keep practicing, and don't be afraid to click the cards to hear the audio whenever you need it.",
  ],
};

// ---------- Registry & helpers ----------
export const OBJECTIVES_REGISTRY: Record<number, LessonObjectives> = {
  1: LESSON_1_OBJECTIVES,
  2: LESSON_2_OBJECTIVES,
  3: LESSON_3_OBJECTIVES,
  4: LESSON_4_OBJECTIVES,
  5: LESSON_5_OBJECTIVES,
  6: LESSON_6_OBJECTIVES,
  7: LESSON_7_OBJECTIVES,
  8: LESSON_8_OBJECTIVES,
  9: LESSON_9_OBJECTIVES,
  10: LESSON_10_OBJECTIVES,
  11: LESSON_11_OBJECTIVES,
  12: LESSON_12_OBJECTIVES,
  13: LESSON_13_OBJECTIVES,
  14: LESSON_14_OBJECTIVES,
  15: LESSON_15_OBJECTIVES,
  16: LESSON_16_OBJECTIVES,
  17: LESSON_17_OBJECTIVES,
  18: LESSON_18_OBJECTIVES,
};

/** Returns objectives for a given lesson or null if none exist. */
export function getObjectivesForLesson(
  lessonNumber: number
): LessonObjectives | null {
  return OBJECTIVES_REGISTRY[lessonNumber] ?? null;
}

/** Convenience helper: is there an objectives entry for this lesson? */
export function hasObjectives(lessonNumber: number): boolean {
  return lessonNumber in OBJECTIVES_REGISTRY;
}

/** Returns a human-friendly fallback if a lesson doesn’t have objectives yet. */
export function getObjectivesFallback(lessonNumber: number): LessonObjectives {
  return {
    lesson: lessonNumber,
    heading: "Student checklist:",
    soFar: [],
    endGoals: [],
    tips: ["Objectives for this lesson are coming soon."],
  };
}
