// app/deepDive/content.ts

// -----------------------------------------------------------------------------
// Audio mapping + helpers
// -----------------------------------------------------------------------------

/**
 * Canonical mapping of Arabic tokens to audio files.
 * This map is now used by multiple surfaces:
 * - Tables with `audioEnabled: true`
 * - Comparison card tokens
 * - Breadcrumb step tokens with `audioEnabled: true`
 *
 * NOTE ON PRECEDENCE: If a token exists in multiple lessons (e.g., 'ك'),
 * the later lesson's mapping takes precedence as the object is constructed.
 * This is intentional to allow context-specific pronunciations.
 */
export const ARABIC_AUDIO_MAP: Record<string, string> = {
  // Lesson 1: Base letters (primary keys)
  ا: "/1.1.wav",
  ب: "/1.2.wav",
  ت: "/1.3.wav",
  ث: "/1.4.wav",
  ج: "/1.5.wav",
  ح: "/1.6.wav",
  خ: "/1.7.wav",
  د: "/1.8.wav",
  ذ: "/1.9.wav",
  ر: "/1.10.wav",
  ز: "/1.11.wav",
  س: "/1.12.wav",
  ش: "/1.13.wav",
  ص: "/1.14.wav",
  ض: "/1.15.wav",
  ط: "/1.16.wav",
  ظ: "/1.17.wav",
  ع: "/1.18.wav",
  غ: "/1.19.wav",
  ف: "/1.20.wav",
  ق: "/1.21.wav",
  ك: "/1.22.wav",
  ل: "/1.23.wav",
  م: "/1.24.wav",
  ن: "/1.25.wav",
  و: "/1.26.wav",
  ه: "/1.27.wav",
  ء: "/1.28.wav",
  ى: "/1.29.wav",
  ي: "/1.30.wav",

  // Lesson 1: Presentation/positional forms
  ﻩ: "/1.27.wav",
  ـﻲ: "/1.30.wav",

  // Lesson 2: Syllables, clusters, and words
  ﻻ: "/2.2.wav",
  ﻼ: "/2.3.wav",
  با: "/2.4.wav",
  بلب: "/2.6.wav",
  كا: "/2.49.wav", // Overwrites 1.22 for context
  بكت: "/2.9.wav",
  نا: "/2.11.wav",
  تا: "/2.12.wav",
  يا: "/2.13.wav",
  بس: "/2.14.wav",
  نس: "/2.15.wav",
  تح: "/2.16.wav",
  يح: "/2.17.wav",
  يم: "/2.18.wav",
  نم: "/2.19.wav",
  ثم: "/2.20.wav",
  بى: "/2.21.wav",
  تى: "/2.22.wav",
  نبل: "/2.23.wav",
  بيل: "/2.24.wav",
  ثثل: "/2.25.wav",
  بنن: "/2.26.wav",
  يتن: "/2.27.wav",
  تحت: "/2.30.wav",
  بت: "/2.31.wav",
  يه: "/2.55.wav", // Overwrites 1.27/1.30 for context
  نت: "/2.33.wav",
  بهم: "/2.34.wav",
  خز: "/2.36.wav",
  تز: "/2.38.wav",
  شل: "/2.40.wav",
  طب: "/2.43.wav",
  ظا: "/2.44.wav",
  عز: "/2.46.wav",
  صع: "/2.47.wav",
  بعد: "/2.48.wav",
  قو: "/2.51.wav",
  ة: "/2.52.wav",
  بة: "/2.54.wav",
  أ: "/2.57.wav",
  ؤء: "/2.58.wav",
  ىء: "/2.59.wav",
  فو: "/2.60.wav",
};

// Characters to strip when normalizing a token for audio lookup
const ARABIC_DIACRITICS_REGEX =
  /[\u0610-\u061A\u064B-\u065F\u06D6-\u06DC\u06DF-\u06E8\u06EA-\u06ED]/g;
const TATWEEL_REGEX = /\u0640/g;

/** Optional alias map (presentation → base) used by the normalizer below. */
export const ARABIC_PRESENTATION_ALIASES: Record<string, string> = {
  ﻩ: "ه",
  ـﻲ: "ي",
};

/**
 * Normalize any clicked token before AUDIO lookup:
 * - remove diacritics + tatweel
 * - map presentation forms to base forms (if known)
 * - trim spacing
 */
export function normalizeArabicTokenForAudio(raw: string): string {
  if (!raw) return raw;
  let s = raw
    .replace(ARABIC_DIACRITICS_REGEX, "")
    .replace(TATWEEL_REGEX, "")
    .trim();
  if (ARABIC_PRESENTATION_ALIASES[s]) s = ARABIC_PRESENTATION_ALIASES[s];
  return s;
}

// -----------------------------------------------------------------------------
// Types
// -----------------------------------------------------------------------------

export type DeepDiveExample = {
  label: string;
  isolated: string;
  initial: string;
  medial: string;
  final: string;
  audioKey?: string;
};

export type DeepDiveTable = {
  title?: string;
  headers: string[];
  rows: string[][];
  rtlColumns?: number[];
  audioEnabled?: boolean;
  audioColumns?: number[];
};

export type DeepDiveHighlight = {
  title: string;
  text: string;
  variant?: "rule" | "example";
  size?: "sm" | "md" | "lg";
  italicArabic?: boolean;
};

/** NEW: Side-by-side comparison card */
export type DeepDiveComparison = {
  titleLeft: string;
  titleRight: string;
  bodyLeft: string;
  bodyRight: string;
  centerLabel?: string;
  audioTokensLeft?: string[];
  audioTokensRight?: string[];
  rtlLeft?: boolean;
  rtlRight?: boolean;
};

/** NEW: Breadcrumb / step sequence */
export type DeepDiveBreadcrumb = {
  title: string;
  steps: Array<{
    label: string;
    token?: string;
    caption?: string;
  }>;
  audioEnabled?: boolean;
  rtl?: boolean;
};

export type DeepDiveEmphasisOptions = {
  wholeWord?: boolean;
  caseSensitive?: boolean;
  respectDiacritics?: boolean;
};

export type DeepDiveEmphasis = {
  bold?: string[];
  italic?: string[];
  options?: DeepDiveEmphasisOptions;
};

export type DeepDiveSection = {
  key: string;
  title: string;
  summary: string;
  bullets?: string[];
  highlights?: DeepDiveHighlight[];
  /** NEW: Side-by-side comparison cards */
  comparisons?: DeepDiveComparison[];
  /** NEW: Breadcrumb/step sequences */
  breadcrumbs?: DeepDiveBreadcrumb[];
  examples?: DeepDiveExample[];
  tables?: DeepDiveTable[];
  emphasis?: DeepDiveEmphasis;
  examplesAudioEnabled?: boolean;
};

export type DeepDiveLesson = {
  lessonNumber: number;
  sections: DeepDiveSection[];
};

// -----------------------------------------------------------------------------
// Content
// -----------------------------------------------------------------------------

export const deepDiveContent: Record<number, DeepDiveLesson> = {
  1: {
    lessonNumber: 1,
    sections: [
      {
        key: "shapes-by-position",
        title: "Letter Shapes by Position",
        summary:
          "Arabic letters connect differently depending on their position within a word. Many letters have four contextual forms: alone, beginning, middle, and end.",
        bullets: [
          "Some letters do not connect to the letter after them.",
          "Practice reading left-to-right in this table, but remember Arabic is written right-to-left.",
          "Focus on the baseline and connection strokes; the core letter often remains recognizable.",
        ],
        highlights: [
          {
            title: "Full alphabet",
            text: "ا ب ت ث ج ح خ د ذ ر ز س ش ص ض ط ظ ع غ ف ق ك ل م ن ه و ي",
            variant: "example",
            size: "md",
            italicArabic: false,
          },
          {
            title: "Non-joining letters",
            text: "ا د ذ ر ز و",
            variant: "example",
            size: "sm",
            italicArabic: false,
          },
        ],
        examples: [
          {
            label: "ا",
            isolated: "ا",
            initial: "◆",
            medial: "◆",
            final: "ـا",
            audioKey: "ا",
          },
          {
            label: "ب",
            isolated: "ب",
            initial: "بـ",
            medial: "ـبـ",
            final: "ـب",
            audioKey: "ب",
          },
          {
            label: "ت",
            isolated: "ت",
            initial: "تـ",
            medial: "ـتـ",
            final: "ـت",
            audioKey: "ت",
          },
          {
            label: "ث",
            isolated: "ث",
            initial: "ثـ",
            medial: "ـثـ",
            final: "ـث",
            audioKey: "ث",
          },
          {
            label: "ج",
            isolated: "ج",
            initial: "جـ",
            medial: "ـجـ",
            final: "ـج",
            audioKey: "ج",
          },
          {
            label: "ح",
            isolated: "ح",
            initial: "حـ",
            medial: "ـحـ",
            final: "ـح",
            audioKey: "ح",
          },
          {
            label: "خ",
            isolated: "خ",
            initial: "خـ",
            medial: "ـخـ",
            final: "ـخ",
            audioKey: "خ",
          },
          {
            label: "د",
            isolated: "د",
            initial: "◆",
            medial: "◆",
            final: "ـد",
            audioKey: "د",
          },
          {
            label: "ذ",
            isolated: "ذ",
            initial: "◆",
            medial: "◆",
            final: "ـذ",
            audioKey: "ذ",
          },
          {
            label: "ر",
            isolated: "ر",
            initial: "◆",
            medial: "◆",
            final: "ـر",
            audioKey: "ر",
          },
          {
            label: "ز",
            isolated: "ز",
            initial: "◆",
            medial: "◆",
            final: "ـز",
            audioKey: "ز",
          },
          {
            label: "س",
            isolated: "س",
            initial: "سـ",
            medial: "ـسـ",
            final: "ـس",
            audioKey: "س",
          },
          {
            label: "ش",
            isolated: "ش",
            initial: "شـ",
            medial: "ـشـ",
            final: "ـش",
            audioKey: "ش",
          },
          {
            label: "ص",
            isolated: "ص",
            initial: "صـ",
            medial: "ـصـ",
            final: "ـص",
            audioKey: "ص",
          },
          {
            label: "ض",
            isolated: "ض",
            initial: "ضـ",
            medial: "ـضـ",
            final: "ـض",
            audioKey: "ض",
          },
          {
            label: "ط",
            isolated: "ط",
            initial: "طـ",
            medial: "ـطـ",
            final: "ـط",
            audioKey: "ط",
          },
          {
            label: "ظ",
            isolated: "ظ",
            initial: "ظـ",
            medial: "ـظـ",
            final: "ـظ",
            audioKey: "ظ",
          },
          {
            label: "ع",
            isolated: "ع",
            initial: "عـ",
            medial: "ـعـ",
            final: "ـع",
            audioKey: "ع",
          },
          {
            label: "غ",
            isolated: "غ",
            initial: "غـ",
            medial: "ـغـ",
            final: "ـغ",
            audioKey: "غ",
          },
          {
            label: "ف",
            isolated: "ف",
            initial: "فـ",
            medial: "ـفـ",
            final: "ـف",
            audioKey: "ف",
          },
          {
            label: "ق",
            isolated: "ق",
            initial: "قـ",
            medial: "ـقـ",
            final: "ـق",
            audioKey: "ق",
          },
          {
            label: "ك",
            isolated: "ك",
            initial: "كـ",
            medial: "ـكـ",
            final: "ـك",
            audioKey: "ك",
          },
          {
            label: "ل",
            isolated: "ل",
            initial: "لـ",
            medial: "ـلـ",
            final: "ـل",
            audioKey: "ل",
          },
          {
            label: "م",
            isolated: "م",
            initial: "مـ",
            medial: "ـمـ",
            final: "ـم",
            audioKey: "م",
          },
          {
            label: "ن",
            isolated: "ن",
            initial: "نـ",
            medial: "ـنـ",
            final: "ـن",
            audioKey: "ن",
          },
          {
            label: "ه",
            isolated: "ه",
            initial: "هـ",
            medial: "ـهـ",
            final: "ـه",
            audioKey: "ه",
          },
          {
            label: "و",
            isolated: "و",
            initial: "◆",
            medial: "◆",
            final: "ـو",
            audioKey: "و",
          },
          {
            label: "ي",
            isolated: "ي",
            initial: "يـ",
            medial: "ـيـ",
            final: "ـي",
            audioKey: "ي",
          },
        ],
        emphasis: {
          bold: [
            "alone",
            "beginning",
            "middle",
            "end",
            "connect",
            "contextual forms",
            "right-to-left",
            "baseline",
            "connection strokes",
            "الحروف",
            "اتصال",
            "ا",
            "د",
            "ذ",
            "ر",
            "ز",
            "و",
          ],
          italic: ["non-joining", "core letter", "table", "word"],
          options: {
            wholeWord: true,
            caseSensitive: false,
            respectDiacritics: false,
          },
        },
        examplesAudioEnabled: true,
      },
      {
        key: "makharij",
        title: "Makharij - Points of Articulation",
        summary:
          "Each letter is produced from a specific point in the mouth or throat. Knowing the makharij sharpens clarity and prevents common mistakes.",
        bullets: [
          "The throat (Halq) set is grouped into upper, middle, and lower positions. See the example tray below.",
          "Tongue groups include sounds formed at the tip, blade, and back of the tongue; compare alveolar contact to the tongue's back against the soft palate.",
          "The lips category covers true bilabial closure and rounded lip articulation without full closure.",
          "Nasal resonance (ghunnah) primarily occurs with nasal consonants when doubled or under specific rules.",
        ],
        highlights: [
          {
            title: "Throat groups (example)",
            text: "ء ه (upper), ع ح (middle), غ خ (lower)",
            variant: "example",
            size: "sm",
            italicArabic: false,
          },
          {
            title: "Principle",
            text: "Makharij are fixed exit points that govern clarity and contrast; train the place of articulation first, then refine timing and airflow",
            variant: "rule",
            size: "sm",
            italicArabic: false,
          },
        ],
        tables: [
          {
            title: "Selected Makharij Examples",
            headers: ["Letter(s)", "Exit (English)", "Common Learner Error"],
            rows: [
              ["ا", "furthest throat", "glottal stop vs. prolongation"],
              [
                "ق ك",
                "tongue's extreme back + soft palate",
                "confusing ق with ك",
              ],
              [
                "ش ج ي",
                "mid-tongue + hard palate",
                "over-curving tongue → distortion",
              ],
            ],
            rtlColumns: [0],
            audioEnabled: true,
            audioColumns: [0],
          },
        ],
        emphasis: {
          bold: [
            "Makharij",
            "Points of Articulation",
            "throat",
            "Halq",
            "tongue",
            "lips",
            "Ghunnah",
            "مخارج",
            "الحلق",
            "اللسان",
            "الشفتان",
            "غنة",
            "أقصى الحلق",
            "أوسط اللسان",
            "الحنك",
            "ق",
            "ك",
            "ش",
            "ج",
            "ي",
            "ء",
            "ه",
            "ع",
            "ح",
            "غ",
            "خ",
            "ب",
            "م",
            "و",
            "ن",
          ],
          italic: [
            "clarity",
            "common mistakes",
            "alveolar",
            "soft palate",
            "hard palate",
            "prolongation",
          ],
          options: {
            wholeWord: true,
            caseSensitive: false,
            respectDiacritics: false,
          },
        },
      },
      {
        key: "heavy-vs-light",
        title: "Heavy vs Light (Tafkhīm vs Tarqīq)",
        summary:
          "Some letters are pronounced with a full, heavy quality (tafkhīm), while others are light (tarqīq). Mastering this contrast is essential for accurate recitation.",
        bullets: [
          "Raa can be heavy or light depending on surrounding vowels and rules; Lam in 'Allah' (lafdh al-jalalah) is often heavy after fatha or damma.",
          "Alif reflects the quality of the preceding letter; it does not carry heaviness by itself.",
          "Listen closely to vowel coloring: heavy letters tend to spread the vowel, while light letters keep it thin.",
        ],
        highlights: [
          {
            title: "Always heavy (example)",
            text: "خ ص ض غ ط ق ظ",
            variant: "example",
            size: "sm",
            italicArabic: false,
          },
        ],
        emphasis: {
          bold: [
            "Heavy",
            "Light",
            "Tafkhīm",
            "Tarqīq",
            "لفظ الجلالة",
            "fatḥah",
            "ḍammah",
            "خ",
            "ص",
            "ض",
            "غ",
            "ط",
            "ق",
            "ظ",
          ],
          italic: [
            "vowel coloring",
            "spread",
            "thin",
            "accurate recitation",
            "surrounding vowels",
          ],
          options: {
            wholeWord: true,
            caseSensitive: false,
            respectDiacritics: false,
          },
        },
      },
      { key: "exam", title: "Exam", summary: "" },
    ],
  },
  2: {
    lessonNumber: 2,
    sections: [
      {
        key: "syllables-and-clusters",
        title: "Syllables and Clusters",
        summary:
          "Arabic words are built from syllables, which are groups of letters blended together. Understanding open and closed syllables is key to correct rhythm and flow.",
        bullets: [
          "An 'open' syllable ends in a vowel sound (e.g., 'ba', 'ka').",
          "A 'closed' syllable ends in a consonant sound (e.g., 'bat', 'nas').",
          "Clusters occur when two consonants appear together without a vowel between them.",
        ],
        highlights: [
          {
            title: "Principle",
            text: "Letters combine into syllables, and syllables combine into words. Identify the pattern of consonants and vowels to master pronunciation.",
            variant: "rule",
            size: "md",
          },
          {
            title: "Examples",
            text: "با + ب → باب  |  ك + ت + ب → كتب",
            variant: "example",
            size: "sm",
          },
        ],
        comparisons: [
          {
            titleLeft: "Open Syllable",
            titleRight: "Closed Syllable",
            bodyLeft:
              "Consonant + Vowel (CV). These create a light, flowing sound.",
            bodyRight:
              "Consonant + Vowel + Consonant (CVC). These feel shorter and more abrupt.",
            centerLabel: "vs",
            audioTokensLeft: ["با", "كا", "نا", "يا"],
            audioTokensRight: ["بت", "نت", "بس", "نس"],
            rtlLeft: true,
            rtlRight: true,
          },
          {
            titleLeft: "Heavy Cluster",
            titleRight: "Light Cluster",
            bodyLeft:
              "Features 'heavy' (tafkhīm) letters, creating a fuller sound.",
            bodyRight:
              "Features 'light' (tarqīq) letters for a thinner, sharper sound.",
            centerLabel: "",
            audioTokensLeft: ["طب", "ظا", "خز", "غ"],
            audioTokensRight: ["تا", "تى", "بى", "يه"],
            rtlLeft: true,
            rtlRight: true,
          },
        ],
        breadcrumbs: [
          {
            title: "Build ‘بت’",
            steps: [
              { label: "Step 1", token: "ب" },
              { label: "Step 2", token: "بت", caption: "add ت" },
            ],
            audioEnabled: true,
            rtl: true,
          },
          {
            title: "Build ‘بلب’",
            steps: [
              { label: "Step 1", token: "ب" },
              { label: "Step 2", token: "بل", caption: "add ل" },
              { label: "Step 3", token: "بلب", caption: "add ب" },
            ],
            audioEnabled: true,
            rtl: true,
          },
          {
            title: "Positional Awareness: ب",
            steps: [
              { label: "Isolated", token: "ب" },
              { label: "Initial", token: "بـ" },
              { label: "Medial", token: "ـبـ" },
              { label: "Final", token: "ـب" },
            ],
            audioEnabled: true,
            rtl: true,
          },
        ],
        tables: [
          {
            title: "Lesson 2 Tokens",
            headers: ["Token (Arabic)", "Audio File"],
            rows: [
              ["ا", "/2.1.wav"],
              ["ﻻ", "/2.2.wav"],
              ["ﻼ", "/2.3.wav"],
              ["با", "/2.4.wav"],
              ["ل", "/2.5.wav"],
              ["بلب", "/2.6.wav"],
              ["ك", "/2.7.wav"],
              ["كا", "/2.8.wav"],
              ["بكت", "/2.9.wav"],
              ["ن", "/2.10.wav"],
              ["نا", "/2.11.wav"],
              ["تا", "/2.12.wav"],
              ["يا", "/2.13.wav"],
              ["بس", "/2.14.wav"],
              ["نس", "/2.15.wav"],
              ["تح", "/2.16.wav"],
              ["يح", "/2.17.wav"],
              ["يم", "/2.18.wav"],
              ["نم", "/2.19.wav"],
              ["ثم", "/2.20.wav"],
              ["بى", "/2.21.wav"],
              ["تى", "/2.22.wav"],
              ["نبل", "/2.23.wav"],
              ["بيل", "/2.24.wav"],
              ["ثثل", "/2.25.wav"],
              ["بنن", "/2.26.wav"],
              ["يتن", "/2.27.wav"],
              ["ج", "/2.28.wav"],
              ["خ", "/2.29.wav"],
              ["تحت", "/2.30.wav"],
              ["بت", "/2.31.wav"],
              ["يه", "/2.32.wav"],
              ["نت", "/2.33.wav"],
              ["بهم", "/2.34.wav"],
              ["ذ", "/2.35.wav"],
              ["خز", "/2.36.wav"],
              ["ز", "/2.37.wav"],
              ["تز", "/2.38.wav"],
              ["ش", "/2.39.wav"],
              ["شل", "/2.40.wav"],
              ["ض", "/2.41.wav"],
              ["ظ", "/2.42.wav"],
              ["طب", "/2.43.wav"],
              ["ظا", "/2.44.wav"],
              ["غ", "/2.45.wav"],
              ["عز", "/2.46.wav"],
              ["صع", "/2.47.wav"],
              ["بعد", "/2.48.wav"],
              ["كا", "/2.49.wav"],
              ["ق", "/2.50.wav"],
              ["قو", "/2.51.wav"],
              ["ة", "/2.52.wav"],
              ["ﻩ", "/2.53.wav"],
              ["بة", "/2.54.wav"],
              ["يه", "/2.55.wav"],
              ["ه", "/2.56.wav"],
              ["أ", "/2.57.wav"],
              ["ؤء", "/2.58.wav"],
              ["ىء", "/2.59.wav"],
              ["فو", "/2.60.wav"],
            ],
            rtlColumns: [0],
            audioEnabled: true,
            audioColumns: [0],
          },
        ],
      },
    ],
  },
};
