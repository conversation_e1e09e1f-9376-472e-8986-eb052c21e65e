// app/deepDive/deepDive.tsx

"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import {
  deepDiveContent,
  DeepDiveLesson,
  DeepDiveTable,
  DeepDiveComparison,
  DeepDiveBreadcrumb,
  ARABIC_AUDIO_MAP,
  normalizeArabicTokenForAudio,
} from "./content";

const INK = "#1a1a18";
const GREY_50 = "rgba(26, 26, 24, 0.04)";
const GREY_100 = "rgba(26, 26, 24, 0.06)";
const GREY_200 = "rgba(26, 26, 24, 0.08)";
const GREY_300 = "rgba(26, 26, 24, 0.12)";
const GREY_500 = "rgba(26, 26, 24, 0.50)";
const GREY_700 = "rgba(26, 26, 24, 0.70)";
const GREY_800 = "rgba(26, 26, 24, 0.80)";

/** ========= Emphasis Utilities (Bold / Italic from content.ts) ========= */

function stripArabicDiacritics(input: string): string {
  return input.replace(
    /[\u0610-\u061A\u064B-\u065F\u06D6-\u06DC\u06DF-\u06E8\u06EA-\u06ED]/g,
    ""
  );
}

function escapeRegex(s: string): string {
  return s.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

type EmphasisOptionsRuntime = {
  wholeWord: boolean;
  caseSensitive: boolean;
  respectDiacritics: boolean;
};

type EmphasisRuntime = {
  options: EmphasisOptionsRuntime;
  boldSet: Set<string>;
  italicSet: Set<string>;
  unionRegex: RegExp | null;
  normalizeForMatch: (s: string) => string;
};

function buildEmphasisRuntime(
  emphasis:
    | {
        bold?: string[];
        italic?: string[];
        options?: {
          wholeWord?: boolean;
          caseSensitive?: boolean;
          respectDiacritics?: boolean;
        };
      }
    | undefined
): EmphasisRuntime | null {
  if (!emphasis || (!emphasis.bold?.length && !emphasis.italic?.length)) {
    return null;
  }
  const opts: EmphasisOptionsRuntime = {
    wholeWord: emphasis.options?.wholeWord ?? true,
    caseSensitive: emphasis.options?.caseSensitive ?? false,
    respectDiacritics: emphasis.options?.respectDiacritics ?? false,
  };
  const normalizeForMatch = (s: string) => {
    let out = opts.respectDiacritics ? s : stripArabicDiacritics(s);
    if (!opts.caseSensitive) out = out.toLowerCase();
    return out;
  };
  const rawTerms = [
    ...(emphasis.bold ?? []),
    ...(emphasis.italic ?? []),
  ].filter(Boolean);
  if (rawTerms.length === 0) return null;
  const uniqueTerms = Array.from(new Set(rawTerms)).sort(
    (a, b) => b.length - a.length
  );
  const normalizedTerms = uniqueTerms.map((t) => normalizeForMatch(t));
  const boldSet = new Set((emphasis.bold ?? []).map(normalizeForMatch));
  const italicSet = new Set((emphasis.italic ?? []).map(normalizeForMatch));
  const boundary = "[\\p{L}\\p{M}\\p{N}_}";
  const left = `(?<!${boundary})`;
  const right = `(?!${boundary})`;
  const unionBody = normalizedTerms.map((t) => escapeRegex(t)).join("|");
  const source = opts.wholeWord
    ? `${left}(?:${unionBody})${right}`
    : `(?:${unionBody})`;
  const flags = `g${opts.caseSensitive ? "" : "i"}u`;
  let unionRegex: RegExp | null = null;
  try {
    unionRegex = new RegExp(source, flags as any);
  } catch {
    const fallbackSource = `(?:${unionBody})`;
    unionRegex = new RegExp(fallbackSource, flags.replace("u", "") as any);
  }
  return { options: opts, boldSet, italicSet, unionRegex, normalizeForMatch };
}

function renderWithEmphasis(
  text: string,
  runtime: EmphasisRuntime | null
): React.ReactNode {
  if (!runtime || !runtime.unionRegex || !text) return text;
  const { unionRegex, normalizeForMatch, boldSet, italicSet } = runtime;
  const orig = text;
  const normChars: string[] = [];
  const normToOrigIdx: number[] = [];
  for (let i = 0; i < orig.length; i++) {
    const ch = orig[i];
    const keep = runtime.options.respectDiacritics
      ? ch
      : ch.replace(
          /[\u0610-\u061A\u064B-\u065F\u06D6-\u06DC\u06DF-\u06E8\u06EA-\u06ED]/g,
          ""
        );
    if (keep.length > 0) {
      for (let k = 0; k < keep.length; k++) {
        normChars.push(keep[k]);
        normToOrigIdx.push(i);
      }
    }
  }
  let normalized = normChars.join("");
  if (!runtime.options.caseSensitive) normalized = normalized.toLowerCase();
  const matches: Array<{
    start: number;
    end: number;
    kind: "bold" | "italic" | "both";
    matchedNorm: string;
  }> = [];
  if (!normalized) return text;
  unionRegex.lastIndex = 0;
  let m: RegExpExecArray | null;
  const consumed: boolean[] = new Array(normalized.length).fill(false);
  while ((m = unionRegex.exec(normalized))) {
    const nStart = m.index;
    const nEnd = nStart + m[0].length;
    let overlaps = false;
    for (let i = nStart; i < nEnd; i++) {
      if (consumed[i]) {
        overlaps = true;
        break;
      }
    }
    if (overlaps) continue;
    const token = normalizeForMatch(m[0]);
    const inBold = boldSet.has(token);
    const inItalic = italicSet.has(token);
    const kind: "bold" | "italic" | "both" =
      inBold && inItalic ? "both" : inBold ? "bold" : "italic";
    const oStart = normToOrigIdx[nStart];
    const oEnd = normToOrigIdx[Math.max(nEnd - 1, nStart)] + 1;
    matches.push({ start: oStart, end: oEnd, kind, matchedNorm: token });
    for (let i = nStart; i < nEnd; i++) consumed[i] = true;
  }
  if (matches.length === 0) return text;
  matches.sort((a, b) => a.start - b.start);
  const out: React.ReactNode[] = [];
  let cursor = 0;
  matches.forEach((seg, idx) => {
    if (cursor < seg.start) {
      out.push(orig.slice(cursor, seg.start));
    }
    const chunk = orig.slice(seg.start, seg.end);
    if (seg.kind === "both") {
      out.push(
        <strong key={`emph-${idx}-b`}>
          <em>{chunk}</em>
        </strong>
      );
    } else if (seg.kind === "bold") {
      out.push(<strong key={`emph-${idx}-b`}>{chunk}</strong>);
    } else {
      out.push(<em key={`emph-${idx}-i`}>{chunk}</em>);
    }
    cursor = seg.end;
  });
  if (cursor < orig.length) out.push(orig.slice(cursor));
  return out;
}

/** ========= Arabic helpers ========= */
const ARABIC_RANGE = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]+/g;

function scaleArabicInString(input: string): React.ReactNode[] {
  if (!input) return [input];
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let m: RegExpExecArray | null;
  ARABIC_RANGE.lastIndex = 0;
  while ((m = ARABIC_RANGE.exec(input))) {
    const [match] = m;
    const start = m.index;
    const end = start + match.length;
    if (lastIndex < start) parts.push(input.slice(lastIndex, start));
    parts.push(
      <span
        key={`ar-${start}`}
        className="font-arabic"
        style={{ fontSize: "1.5em", lineHeight: "inherit" }}
        dir="auto"
      >
        {match}
      </span>
    );
    lastIndex = end;
  }
  if (lastIndex < input.length) parts.push(input.slice(lastIndex));
  return parts;
}

function scaleArabicInNode(node: React.ReactNode): React.ReactNode {
  if (typeof node === "string") return scaleArabicInString(node);
  if (Array.isArray(node))
    return node.map((child, idx) => (
      <React.Fragment key={idx}>{scaleArabicInNode(child)}</React.Fragment>
    ));
  if (React.isValidElement(node)) {
    const props: any = node.props ?? {};
    const children = props.children;
    if (children == null) return node;
    const newChildren = scaleArabicInNode(children);
    return React.cloneElement(node, { ...props }, newChildren);
  }
  return node;
}

function wrapArabicForTrayInString(
  text: string,
  fontSizeEm = "1.18em"
): React.ReactNode[] {
  if (!text) return [text];
  const out: React.ReactNode[] = [];
  let last = 0;
  let m: RegExpExecArray | null;
  ARABIC_RANGE.lastIndex = 0;
  while ((m = ARABIC_RANGE.exec(text))) {
    const start = m.index;
    const end = start + m[0].length;
    if (last < start) out.push(text.slice(last, start));
    out.push(
      <span
        key={`tray-ar-${start}`}
        className="font-arabic"
        style={{
          fontStyle: "normal",
          fontSize: fontSizeEm,
          lineHeight: "inherit",
          unicodeBidi: "isolate",
        }}
        dir="rtl"
      >
        {m[0]}
      </span>
    );
    last = end;
  }
  if (last < text.length) out.push(text.slice(last));
  return out;
}

function wrapArabicForTrayInNode(
  node: React.ReactNode,
  fontSizeEm = "1.18em"
): React.ReactNode {
  if (typeof node === "string")
    return wrapArabicForTrayInString(node, fontSizeEm);
  if (Array.isArray(node))
    return node.map((child, idx) => (
      <React.Fragment key={idx}>
        {wrapArabicForTrayInNode(child, fontSizeEm)}
      </React.Fragment>
    ));
  if (React.isValidElement(node)) {
    const props: any = node.props ?? {};
    const newChildren = wrapArabicForTrayInNode(props.children, fontSizeEm);
    return React.cloneElement(node, { ...props }, newChildren);
  }
  return node;
}

function wrapArabicForTableInString(
  text: string,
  fontSizeEm = "2em"
): React.ReactNode[] {
  if (!text) return [text];
  const out: React.ReactNode[] = [];
  let last = 0;
  let m: RegExpExecArray | null;
  ARABIC_RANGE.lastIndex = 0;
  while ((m = ARABIC_RANGE.exec(text))) {
    const start = m.index;
    const end = start + m[0].length;
    if (last < start) out.push(text.slice(last, start));
    out.push(
      <span
        key={`tbl-ar-${start}`}
        className="font-arabic"
        style={{
          fontStyle: "normal",
          fontSize: fontSizeEm,
          lineHeight: "inherit",
          unicodeBidi: "isolate",
        }}
        dir="rtl"
      >
        {m[0]}
      </span>
    );
    last = end;
  }
  if (last < text.length) out.push(text.slice(last));
  return out;
}

function wrapArabicForTableInNode(
  node: React.ReactNode,
  fontSizeEm = "2em"
): React.ReactNode {
  if (typeof node === "string")
    return wrapArabicForTableInString(node, fontSizeEm);
  if (Array.isArray(node))
    return node.map((child, idx) => (
      <React.Fragment key={idx}>
        {wrapArabicForTableInNode(child, fontSizeEm)}
      </React.Fragment>
    ));
  if (React.isValidElement(node)) {
    const props: any = node.props ?? {};
    const newChildren = wrapArabicForTableInNode(props.children, fontSizeEm);
    return React.cloneElement(node, { ...props }, newChildren);
  }
  return node;
}

function renderTrayText(
  text: string,
  variant: "rule" | "example" | undefined,
  italicArabic: boolean | undefined,
  emphasisRuntime: EmphasisRuntime | null
): React.ReactNode {
  const sizeBump = "1.18em";
  if (variant === "example") return wrapArabicForTrayInString(text, sizeBump);
  const emphasized = renderWithEmphasis(text, emphasisRuntime);
  return wrapArabicForTrayInNode(emphasized, sizeBump);
}

function FilledTriangle({
  size = 12,
  color = INK,
  className,
  selected = false,
}: {
  size?: number;
  color?: string;
  className?: string;
  selected?: boolean;
}) {
  return (
    <motion.svg
      viewBox="0 0 24 24"
      width={size}
      height={size}
      className={className}
      aria-hidden
      focusable="false"
      initial={false}
      animate={{ x: selected ? 2 : 0, rotate: selected ? 8 : 0 }}
      transition={{ duration: 0.16, ease: "easeOut" }}
    >
      <polygon points="6,4 20,12 6,20" style={{ fill: color }} />
    </motion.svg>
  );
}

function DiamondPlaceholder({
  size = 10,
  color = GREY_300,
  className,
}: {
  size?: number;
  color?: string;
  className?: string;
}) {
  return (
    <span
      className={`inline-flex items-center justify-center ${className || ""}`}
      style={{
        width: size,
        height: size,
        transform: "rotate(45deg)",
        backgroundColor: color,
        flexShrink: 0,
      }}
      aria-label="Not applicable"
      role="presentation"
    />
  );
}

type DeepDiveProps = {
  lessonNumber?: number;
  className?: string;
  initialSectionKey?: string;
};

export default function DeepDive({
  lessonNumber = 1,
  className,
  initialSectionKey,
}: DeepDiveProps) {
  const lesson: DeepDiveLesson | undefined = deepDiveContent[lessonNumber];
  const [activeKey, setActiveKey] = React.useState<string | null>(
    initialSectionKey ?? null
  );
  React.useEffect(() => {
    setActiveKey(initialSectionKey ?? null);
  }, [lessonNumber, initialSectionKey]);
  const buttonRefs = React.useRef<Array<HTMLButtonElement | null>>([]);
  const audioRef = React.useRef<HTMLAudioElement | null>(null);

  /**
   * Single shared audio player for all interactive surfaces.
   * - Normalizes the token for robust lookup.
   * - Reuses a single HTMLAudioElement to prevent concurrent playback issues.
   * - No autoplay; only plays on explicit user action.
   */
  const playAudioByKey = React.useCallback((key: string | null | undefined) => {
    if (!key) return;
    const normalized = normalizeArabicTokenForAudio(key);
    const src = ARABIC_AUDIO_MAP[normalized] ?? ARABIC_AUDIO_MAP[key] ?? null;
    if (!src) return;
    if (!audioRef.current) {
      audioRef.current = new Audio();
      audioRef.current.preload = "auto";
    }
    const a = audioRef.current;
    try {
      a.pause();
      a.src = src;
      a.currentTime = 0;
      void a.play();
    } catch (err) {
      console.warn("[DeepDive] Audio play failed:", err);
    }
  }, []);

  const activeSection =
    lesson?.sections.find((s) => s.key === activeKey) ?? null;
  const emphasisRuntime = React.useMemo(
    () => buildEmphasisRuntime(activeSection?.emphasis),
    [activeSection]
  );

  if (process.env.NODE_ENV !== "production") {
    if (activeSection?.summary && ARABIC_RANGE.test(activeSection.summary)) {
      console.warn(
        "[DeepDive] Arabic detected in summary. Sentences must be English-only."
      );
    }
    activeSection?.bullets?.forEach((b, i) => {
      if (ARABIC_RANGE.test(b)) {
        console.warn(
          `[DeepDive] Arabic detected in bullet #${
            i + 1
          }. Move Arabic to an example tray.`
        );
      }
    });
  }

  if (!lesson) {
    return (
      <section
        className={[
          "w-full h-full overflow-y-auto bg-transparent p-6 md:p-8",
          className || "",
        ].join(" ")}
      >
        <div className="flex items-start gap-6">
          <HeaderBadge number={lessonNumber} />
          <p style={{ color: GREY_500 }}>No deep dive content found.</p>
        </div>
      </section>
    );
  }

  const isFocused = Boolean(activeKey);
  const dockTransition = { duration: 0.26, ease: [0.34, 0.08, 0.22, 1] };
  const contentTransition = { duration: 0.2, delay: 0.06, ease: "easeOut" };

  const onKeyDownRail = (e: React.KeyboardEvent<HTMLDivElement>) => {
    const count = lesson.sections.length;
    if (count === 0) return;
    const indexFromKey = lesson.sections.findIndex((s) => s.key === activeKey);
    const currentIndex =
      indexFromKey === -1 ? Math.max(0, Math.min(count - 1, 0)) : indexFromKey;
    const focusAt = (i: number) => {
      if (buttonRefs.current[i]) buttonRefs.current[i]!.focus();
    };
    switch (e.key) {
      case "ArrowDown":
      case "j":
        e.preventDefault();
        focusAt((currentIndex + 1) % count);
        break;
      case "ArrowUp":
      case "k":
        e.preventDefault();
        focusAt((currentIndex - 1 + count) % count);
        break;
      case "Home":
        e.preventDefault();
        focusAt(0);
        break;
      case "End":
        e.preventDefault();
        focusAt(count - 1);
        break;
      case "Enter":
      case " ":
        const i = buttonRefs.current.findIndex(
          (n) => n === document.activeElement
        );
        if (i >= 0) {
          const key = lesson.sections[i].key;
          setActiveKey((p) => (p === key ? null : key));
        }
        break;
      default:
        break;
    }
  };

  const listVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.08 } },
  };
  const itemVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.3, ease: "easeOut" },
    },
  };

  return (
    <section
      className={[
        "w-full h-full flex transition-all duration-300 ease-in-out",
        className || "",
      ].join(" ")}
      aria-labelledby={`deep-dive-lesson-${lesson.lessonNumber}`}
      style={{ gap: isFocused ? "48px" : "24px" }}
    >
      <div
        className="flex-shrink-0 flex flex-col p-6 md:p-10 transition-all duration-300 ease-in-out"
        style={{ width: isFocused ? "340px" : "100%" }}
      >
        <motion.aside
          animate={{ scale: isFocused ? 0.96 : 1, x: 0, y: 0 }}
          transition={dockTransition}
          style={{ transformOrigin: "top left" }}
          className="mx-auto md:mx-0"
          role="navigation"
          aria-label="Dive Deeper topics"
          onKeyDown={onKeyDownRail}
        >
          <div className="flex items-start gap-4">
            <HeaderBadge number={lesson.lessonNumber} compact={isFocused} />
          </div>
          <nav
            aria-label="Deep Dive Sections"
            className={`${isFocused ? "mt-6" : "mt-10"} pl-6 relative`}
          >
            <span
              aria-hidden
              className="absolute left-0 top-1 bottom-1 w-[1.5px]"
              style={{ backgroundColor: GREY_200 }}
            />
            <motion.ul
              className={isFocused ? "space-y-3" : "space-y-5"}
              role="tablist"
              aria-orientation="vertical"
              variants={listVariants}
              initial="hidden"
              animate="visible"
            >
              {lesson.sections.map((section, i) => {
                const selected = activeKey === section.key;
                return (
                  <motion.li
                    key={section.key}
                    className="list-none"
                    role="presentation"
                    variants={itemVariants}
                  >
                    <button
                      type="button"
                      role="tab"
                      ref={(el) => {
                        buttonRefs.current[i] = el;
                      }}
                      onClick={() =>
                        setActiveKey((p) =>
                          p === section.key ? null : section.key
                        )
                      }
                      aria-current={selected ? "true" : undefined}
                      aria-selected={selected}
                      aria-controls={`deep-dive-panel-${section.key}`}
                      className={`group inline-flex items-center gap-2.5 font-medium leading-snug hover:opacity-70 focus:outline-none transition-all duration-150 focus-visible:ring-2 focus-visible:ring-offset-2 rounded px-1 py-0.5`}
                      style={{
                        fontSize: isFocused ? "14px" : "20px",
                        letterSpacing: "-0.01em",
                        color: selected ? INK : GREY_800,
                        ["--tw-ring-color" as any]: GREY_300,
                        fontWeight: selected ? 600 : 500,
                      }}
                    >
                      <FilledTriangle
                        size={isFocused ? 10 : 14}
                        color={selected ? INK : GREY_500}
                        selected={selected}
                        className="flex-shrink-0 translate-y-[0.5px] group-hover:translate-x-[2px] transition-transform duration-150"
                      />
                      <span>{section.title}</span>
                    </button>
                  </motion.li>
                );
              })}
            </motion.ul>
          </nav>
        </motion.aside>
        <AnimatePresence>
          {isFocused && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="mt-auto"
            >
              <Image
                src="/Womanlearning.svg"
                alt="A woman engaged in learning."
                width={300}
                height={230}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      <main
        className={`flex-1 min-w-0 overflow-y-auto ${
          isFocused ? "block" : "hidden"
        }`}
        aria-live="polite"
      >
        <div className="p-6 md:p-10">
          <AnimatePresence mode="wait">
            {!activeSection ? (
              <motion.div
                key="hint"
                id="deep-dive-hint"
                initial={{ opacity: 0, y: 6 }}
                animate={{ opacity: 0.6, y: 0 }}
                exit={{ opacity: 0, y: -6 }}
                transition={{ duration: 0.2 }}
                className="text-sm"
                style={{ color: GREY_500 }}
              >
                Select a topic on the left to dive in.
              </motion.div>
            ) : (
              <motion.article
                key={activeSection.key}
                id={`deep-dive-panel-${activeSection.key}`}
                role="tabpanel"
                initial={{ opacity: 0, y: 12 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -8 }}
                transition={contentTransition}
                className="max-w-[1000px] mx-auto px-2"
                aria-labelledby={`deep-dive-title-${activeSection.key}`}
              >
                <h2
                  id={`deep-dive-title-${activeSection.key}`}
                  className="text-[28px] md:text-[32px] font-semibold leading-tight mb-3"
                  style={{ letterSpacing: "-0.02em", color: INK }}
                >
                  {activeSection.title}
                </h2>
                {activeSection.summary && (
                  <p
                    className="mt-4 text-[15px] md:text-[16px] leading-relaxed"
                    style={{ color: GREY_700, lineHeight: "1.6" }}
                  >
                    {scaleArabicInNode(
                      renderWithEmphasis(activeSection.summary, emphasisRuntime)
                    )}
                  </p>
                )}
                {activeSection.bullets?.length ? (
                  <ul
                    className="mt-6 space-y-2.5 text-[15px] md:text-[16px]"
                    style={{ color: INK, lineHeight: "1.6" }}
                  >
                    {activeSection.bullets.map((b, i) => (
                      <li key={i} className="flex gap-3">
                        <span
                          style={{
                            color: GREY_500,
                            fontSize: "8px",
                            marginTop: "9px",
                            flexShrink: 0,
                          }}
                        >
                          ●
                        </span>
                        <span>
                          {scaleArabicInNode(
                            renderWithEmphasis(b, emphasisRuntime)
                          )}
                        </span>
                      </li>
                    ))}
                  </ul>
                ) : null}

                {/* Integration order: Trays -> Comparisons -> Breadcrumbs -> Examples -> Tables */}

                {activeSection.highlights?.length ? (
                  <div className="mt-6 space-y-3">
                    {activeSection.highlights.map((hl, idx) => (
                      <HighlightTray
                        key={idx}
                        title={hl.title}
                        text={hl.text}
                        variant={hl.variant ?? "example"}
                        size={hl.size ?? "sm"}
                        italicArabic={
                          hl.italicArabic ?? hl.variant === "example"
                        }
                        emphasisRuntime={emphasisRuntime}
                      />
                    ))}
                  </div>
                ) : null}

                {activeSection.comparisons?.length ? (
                  <div className="mt-10">
                    <ComparisonCards
                      comparisons={activeSection.comparisons}
                      onPlayToken={playAudioByKey}
                      renderEmphasis={(s) =>
                        renderWithEmphasis(s, emphasisRuntime)
                      }
                      emphasisRuntime={emphasisRuntime}
                    />
                  </div>
                ) : null}

                {activeSection.breadcrumbs?.length ? (
                  <div className="mt-10">
                    <BreadcrumbSteps
                      breadcrumbs={activeSection.breadcrumbs}
                      onPlayToken={playAudioByKey}
                    />
                  </div>
                ) : null}

                {activeSection.examples?.length ? (
                  <div className="mt-10 overflow-x-auto min-w-0">
                    <div
                      className="rounded-lg overflow-hidden border"
                      style={{ borderColor: GREY_200 }}
                    >
                      <table className="w-full border-collapse">
                        <thead
                          className="text-xs font-medium tracking-wide"
                          style={{ background: GREY_50, color: GREY_700 }}
                        >
                          <tr>
                            <th className="py-2.5 px-4 text-center font-medium">
                              End
                            </th>
                            <th
                              className="py-2.5 px-4 text-center border-l font-medium"
                              style={{ borderColor: GREY_200 }}
                            >
                              Middle
                            </th>
                            <th
                              className="py-2.5 px-4 text-center border-l font-medium"
                              style={{ borderColor: GREY_200 }}
                            >
                              Beginning
                            </th>
                            <th
                              className="py-2.5 px-4 text-center border-l font-medium"
                              style={{ borderColor: GREY_200 }}
                            >
                              Alone
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {activeSection.examples.map((ex, i) => {
                            const audioKey = ex.audioKey ?? ex.label;
                            const interactive =
                              Boolean(activeSection.examplesAudioEnabled) &&
                              Boolean(
                                ARABIC_AUDIO_MAP[
                                  normalizeArabicTokenForAudio(audioKey)
                                ] || ARABIC_AUDIO_MAP[audioKey]
                              );
                            const click = interactive
                              ? () => playAudioByKey(audioKey)
                              : undefined;
                            return (
                              <motion.tr
                                key={i}
                                className="border-t transition-colors duration-150"
                                style={{ borderColor: GREY_200 }}
                                whileHover={{ backgroundColor: GREY_50 }}
                              >
                                <HoverLetterCell
                                  text={ex.final}
                                  withDivider={false}
                                  onClick={click}
                                  interactive={interactive}
                                  ariaLabel={`Play ${audioKey}`}
                                />
                                <HoverLetterCell
                                  text={ex.medial}
                                  withDivider
                                  onClick={click}
                                  interactive={interactive}
                                  ariaLabel={`Play ${audioKey}`}
                                />
                                <HoverLetterCell
                                  text={ex.initial}
                                  withDivider
                                  onClick={click}
                                  interactive={interactive}
                                  ariaLabel={`Play ${audioKey}`}
                                />
                                <HoverLetterCell
                                  text={ex.isolated}
                                  withDivider
                                  onClick={click}
                                  interactive={interactive}
                                  ariaLabel={`Play ${audioKey}`}
                                />
                              </motion.tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : null}

                {activeSection.tables?.length ? (
                  <div className="mt-10 space-y-8">
                    {activeSection.tables.map((t, i) => (
                      <DataTable
                        key={i}
                        table={t}
                        renderEmphasis={(s) =>
                          renderWithEmphasis(s, emphasisRuntime)
                        }
                        onPlayToken={playAudioByKey}
                      />
                    ))}
                  </div>
                ) : null}
              </motion.article>
            )}
          </AnimatePresence>
        </div>
      </main>
    </section>
  );
}

function HeaderBadge({
  number,
  compact = false,
}: {
  number: number;
  compact?: boolean;
}) {
  return (
    <div className="flex items-start gap-3.5 select-none">
      <div
        className={`${
          compact ? "text-[64px]" : "text-[80px]"
        } font-bold leading-[0.9] tabular-nums`}
        style={{ letterSpacing: "-0.03em", color: INK }}
      >
        {String(number).padStart(2, "0")}
      </div>
      <div
        className="text-[16px] leading-snug font-medium"
        style={{ color: GREY_700, letterSpacing: "0.01em", marginTop: "4px" }}
      >
        <div>Dive</div>
        <div>Deeper</div>
      </div>
    </div>
  );
}

function HoverLetterCell({
  text,
  withDivider = false,
  onClick,
  interactive = false,
  ariaLabel,
}: {
  text: string;
  withDivider?: boolean;
  onClick?: () => void;
  interactive?: boolean;
  ariaLabel?: string;
}) {
  const isPlaceholder = text === "◆";
  const handleKey = (e: React.KeyboardEvent<HTMLSpanElement>) => {
    if (
      interactive &&
      onClick &&
      !isPlaceholder &&
      (e.key === "Enter" || e.key === " ")
    ) {
      e.preventDefault();
      onClick();
    }
  };
  return (
    <td
      className={`py-3 px-4 ${
        withDivider ? "border-l" : ""
      } text-center align-middle`}
      style={withDivider ? { borderColor: GREY_200 } : undefined}
    >
      {isPlaceholder ? (
        <div className="flex items-center justify-center gap-1">
          {Array.from({ length: 6 }).map((_, idx) => (
            <DiamondPlaceholder key={idx} size={6} color={GREY_300} />
          ))}
        </div>
      ) : (
        <motion.span
          className={`inline-block font-arabic text-[26px] select-none rounded px-0 ${
            interactive ? "cursor-pointer" : "cursor-default"
          }`}
          style={{
            color: INK,
            transformOrigin: "center",
            outline: "none",
            fontStyle: "normal",
          }}
          whileHover={interactive ? { x: 2, scale: 1.06, color: INK } : {}}
          transition={{ duration: 0.15, ease: "easeOut" }}
          dir="rtl"
          role={interactive ? "button" : undefined}
          tabIndex={interactive ? 0 : -1}
          aria-label={interactive ? ariaLabel : undefined}
          onClick={interactive ? onClick : undefined}
          onKeyDown={handleKey}
        >
          {text}
        </motion.span>
      )}
    </td>
  );
}

function HighlightTray({
  title,
  text,
  variant,
  size,
  italicArabic,
  emphasisRuntime,
}: {
  title: string;
  text: string;
  variant: "rule" | "example";
  size: "sm" | "md" | "lg";
  italicArabic: boolean;
  emphasisRuntime: EmphasisRuntime | null;
}) {
  const pads =
    size === "lg"
      ? {
          px: "px-8 md:px-10",
          py: "py-7 md:py-8",
          content: "text-[18px] md:text-[20px]",
        }
      : size === "md"
      ? {
          px: "px-7 md:px-8",
          py: "py-6",
          content: "text-[16px] md:text-[18px]",
        }
      : { px: "px-6", py: "py-4", content: "text-[16px] md:text-[18px]" };
  const isRule = variant === "rule";
  const trayContentClass = isRule ? "text-[15px] md:text-[16px]" : pads.content;
  return (
    <motion.div
      role={isRule ? "note" : "group"}
      dir="auto"
      className={`w-full rounded-lg border transition-colors duration-200 cursor-default ${pads.px} ${pads.py} select-text`}
      style={{
        background: GREY_50,
        borderColor: GREY_200,
        color: INK,
        lineHeight: "1.7",
      }}
      whileHover={{ backgroundColor: GREY_100, borderColor: GREY_300 }}
    >
      <div
        className="text-[11px] font-medium uppercase tracking-wider mb-2.5"
        style={{ color: GREY_700, letterSpacing: "0.08em" }}
      >
        {title}
      </div>
      <div className={trayContentClass} style={{ direction: "rtl" }}>
        {renderTrayText(text, variant, italicArabic, emphasisRuntime)}
      </div>
    </motion.div>
  );
}

/** ======== NEW: ComparisonCards ======== */
function ComparisonCards({
  comparisons,
  onPlayToken,
  emphasisRuntime,
}: {
  comparisons: DeepDiveComparison[];
  onPlayToken?: (token: string) => void;
  renderEmphasis?: (s: string) => React.ReactNode;
  emphasisRuntime: EmphasisRuntime | null;
}) {
  const handleTokenKey =
    (token: string) => (e: React.KeyboardEvent<HTMLSpanElement>) => {
      if (!onPlayToken) return;
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        onPlayToken(token);
      }
    };
  return (
    <div className="space-y-8">
      {comparisons.map((comp, idx) => (
        <div
          key={idx}
          className="flex w-full flex-col lg:flex-row items-stretch gap-4"
        >
          {/* Left Card */}
          <div
            className="card w-full bg-base-100 border flex-1"
            style={{ borderColor: GREY_200, background: GREY_50 }}
            dir={comp.rtlLeft ? "rtl" : "ltr"}
          >
            <div className="card-body p-6">
              <h3
                className="card-title text-sm font-semibold"
                style={{ color: INK }}
              >
                {comp.titleLeft}
              </h3>
              <p className="text-sm" style={{ color: GREY_800 }}>
                {renderWithEmphasis(comp.bodyLeft, emphasisRuntime)}
              </p>
              {comp.audioTokensLeft && (
                <div
                  className="card-actions justify-start mt-4 flex flex-row flex-wrap gap-2"
                  dir="rtl"
                >
                  {comp.audioTokensLeft.map((token) => (
                    <span
                      key={token}
                      role="button"
                      tabIndex={0}
                      aria-label={`Play ${token}`}
                      onClick={() => onPlayToken?.(token)}
                      onKeyDown={handleTokenKey(token)}
                      className="font-arabic text-4xl p-1 rounded-md cursor-pointer focus:outline-none focus-visible:ring-2"
                      style={{
                        fontStyle: "normal",
                        ["--tw-ring-color" as any]: GREY_300,
                      }}
                    >
                      {token}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
          {/* Divider */}
          <div
            className="divider lg:divider-horizontal uppercase text-xs font-medium tracking-widest"
            style={{ color: GREY_500 }}
          >
            {comp.centerLabel}
          </div>
          {/* Right Card */}
          <div
            className="card w-full bg-base-100 border flex-1"
            style={{ borderColor: GREY_200, background: GREY_50 }}
            dir={comp.rtlRight ? "rtl" : "ltr"}
          >
            <div className="card-body p-6">
              <h3
                className="card-title text-sm font-semibold"
                style={{ color: INK }}
              >
                {comp.titleRight}
              </h3>
              <p className="text-sm" style={{ color: GREY_800 }}>
                {renderWithEmphasis(comp.bodyRight, emphasisRuntime)}
              </p>
              {comp.audioTokensRight && (
                <div
                  className="card-actions justify-start mt-4 flex flex-row flex-wrap gap-2"
                  dir="rtl"
                >
                  {comp.audioTokensRight.map((token) => (
                    <span
                      key={token}
                      role="button"
                      tabIndex={0}
                      aria-label={`Play ${token}`}
                      onClick={() => onPlayToken?.(token)}
                      onKeyDown={handleTokenKey(token)}
                      className="font-arabic text-4xl p-1 rounded-md cursor-pointer focus:outline-none focus-visible:ring-2"
                      style={{
                        fontStyle: "normal",
                        ["--tw-ring-color" as any]: GREY_300,
                      }}
                    >
                      {token}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

/** ======== NEW: BreadcrumbSteps ======== */
function BreadcrumbSteps({
  breadcrumbs,
  onPlayToken,
}: {
  breadcrumbs: DeepDiveBreadcrumb[];
  onPlayToken?: (token: string) => void;
}) {
  const handleStepKey =
    (token: string | undefined) =>
    (e: React.KeyboardEvent<HTMLButtonElement>) => {
      if (!token || !onPlayToken) return;
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        onPlayToken(token);
      }
    };
  return (
    <div className="space-y-10">
      {breadcrumbs.map((bc, idx) => (
        <div key={idx}>
          <h3 className="mb-4 text-base font-semibold" style={{ color: INK }}>
            {bc.title}
          </h3>
          <div className="text-sm">
            <div className="flex items-start gap-2 overflow-x-auto pb-2">
              {bc.steps.map((step, sIdx) => {
                const interactive = bc.audioEnabled && !!step.token;
                const Node = interactive ? "button" : "div";
                return (
                  <React.Fragment key={sIdx}>
                    <Node
                      className={`flex flex-col items-center text-center p-2 rounded-lg transition-colors duration-150 ${
                        interactive
                          ? "cursor-pointer hover:bg-gray-100 focus:outline-none focus-visible:ring-2"
                          : ""
                      }`}
                      style={{
                        minWidth: "80px",
                        ["--tw-ring-color" as any]: GREY_300,
                      }}
                      onClick={
                        interactive
                          ? () => onPlayToken?.(step.token)
                          : undefined
                      }
                      onKeyDown={
                        interactive ? handleStepKey(step.token) : undefined
                      }
                      aria-label={
                        interactive ? `Play ${step.token}` : undefined
                      }
                    >
                      <span className="text-xs" style={{ color: GREY_700 }}>
                        {step.label}
                      </span>
                      {step.token && (
                        <span
                          className="font-arabic text-4xl mt-1"
                          style={{ fontStyle: "normal" }}
                          dir={bc.rtl ? "rtl" : "ltr"}
                        >
                          {step.token}
                        </span>
                      )}
                      {step.caption && (
                        <span
                          className="text-xs mt-1"
                          style={{ color: GREY_500 }}
                        >
                          {step.caption}
                        </span>
                      )}
                    </Node>
                    {sIdx < bc.steps.length - 1 && (
                      <div className="self-center pt-5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          style={{ color: GREY_300 }}
                        >
                          <path d="M9 18l6-6-6-6" />
                        </svg>
                      </div>
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

function DataTable({
  table,
  renderEmphasis,
  onPlayToken,
}: {
  table: DeepDiveTable;
  renderEmphasis?: (s: string) => React.ReactNode;
  onPlayToken?: (token: string) => void;
}) {
  const isRTL = React.useCallback(
    (colIdx: number) => !!table.rtlColumns?.includes(colIdx),
    [table.rtlColumns]
  );
  const audioEnabled = Boolean(table.audioEnabled);
  const audioColumns = React.useMemo(
    () => new Set<number>(table.audioColumns ?? []),
    [table.audioColumns]
  );
  const tokenHasAudio = React.useCallback((rawToken: string) => {
    const norm = normalizeArabicTokenForAudio(rawToken);
    return Boolean(ARABIC_AUDIO_MAP[norm] ?? ARABIC_AUDIO_MAP[rawToken]);
  }, []);
  const splitTokens = React.useCallback((cell: string) => {
    return cell.split(/(\s+)/);
  }, []);
  const handleTokenKey =
    (token: string, playable: boolean) =>
    (e: React.KeyboardEvent<HTMLSpanElement>) => {
      if (playable && onPlayToken && (e.key === "Enter" || e.key === " ")) {
        e.preventDefault();
        onPlayToken(token);
      }
    };
  const isArabic = (s: string) =>
    /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]/.test(s);
  return (
    <div className="overflow-x-auto min-w-0">
      {table.title && (
        <h3
          className="mb-4 text-[18px] font-semibold"
          style={{ color: INK, letterSpacing: "-0.01em" }}
        >
          {table.title}
        </h3>
      )}
      <div
        className="rounded-lg overflow-hidden border"
        style={{ borderColor: GREY_200 }}
      >
        <table className="w-full border-collapse">
          <thead
            className="text-xs font-medium tracking-wide"
            style={{ background: GREY_50, color: GREY_700 }}
          >
            <tr>
              {table.headers.map((h, i) => (
                <th
                  key={i}
                  scope="col"
                  className={`py-2.5 px-4 text-left font-medium ${
                    i > 0 ? "border-l" : ""
                  }`}
                  style={i > 0 ? { borderColor: GREY_200 } : undefined}
                  dir={isRTL(i) ? "rtl" : "auto"}
                >
                  {h}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {table.rows.map((row, rIdx) => (
              <motion.tr
                key={rIdx}
                className="border-t transition-colors duration-150"
                style={{ borderColor: GREY_200 }}
                whileHover={{ backgroundColor: GREY_50 }}
              >
                {row.map((cell, cIdx) => {
                  const rtl = isRTL(cIdx);
                  const isAudioCol =
                    audioEnabled && audioColumns.has(cIdx) && !!onPlayToken;
                  return (
                    <td
                      key={cIdx}
                      className={`py-2.5 px-4 align-top text-[14px] ${
                        cIdx > 0 ? "border-l" : ""
                      } ${rtl ? "font-arabic text-[16px]" : ""}`}
                      style={{
                        borderColor: cIdx > 0 ? GREY_200 : undefined,
                        color: INK,
                        lineHeight: "1.5",
                      }}
                      dir={rtl ? "rtl" : "auto"}
                    >
                      {isAudioCol
                        ? splitTokens(cell).map((tok, i) => {
                            const playable =
                              tok.trim().length > 0 && tokenHasAudio(tok);
                            const arabic = isArabic(tok);
                            if (playable) {
                              return (
                                <span
                                  key={i}
                                  role="button"
                                  tabIndex={0}
                                  aria-label={`Play ${tok}`}
                                  className="inline-block select-none rounded px-0.5 cursor-pointer"
                                  style={{
                                    outline: "none",
                                    fontStyle: "normal",
                                    fontSize: arabic ? "2em" : undefined,
                                  }}
                                  onClick={() => onPlayToken?.(tok)}
                                  onKeyDown={handleTokenKey(tok, playable)}
                                  dir={arabic ? "rtl" : undefined}
                                >
                                  <span
                                    className={
                                      arabic ? "font-arabic" : undefined
                                    }
                                  >
                                    {tok}
                                  </span>
                                </span>
                              );
                            }
                            return arabic ? (
                              <span
                                key={i}
                                className="font-arabic inline-block"
                                style={{
                                  fontStyle: "normal",
                                  fontSize: "2em",
                                  lineHeight: "inherit",
                                  unicodeBidi: "isolate",
                                }}
                                dir="rtl"
                              >
                                {tok}
                              </span>
                            ) : (
                              <React.Fragment key={i}>{tok}</React.Fragment>
                            );
                          })
                        : (() => {
                            const node = renderEmphasis
                              ? renderEmphasis(cell)
                              : cell;
                            return wrapArabicForTableInNode(node, "2em");
                          })()}
                    </td>
                  );
                })}
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
