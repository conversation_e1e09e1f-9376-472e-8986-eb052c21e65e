"use client";

import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

/**
 * SpeakSequence.tsx
 * Continuous mic capture → chunked POSTs → grade → advance on match
 * Minimal UI to match screenshot: big centered target + bottom mic bar.
 *
 * ENHANCEMENT: Client-side WAV chunking (16 kHz mono) so the server/HF accept tiny chunks.
 * We keep your MediaRecorder path as a fallback; primary path uses Web Audio.
 * NEW (this commit):
 *  - Default chunk/send timings to chunkMs=1200, sendIntervalMs=300
 *  - Mini-VAD: skip near-silence chunks, but send a keepalive if no speech for ~1.2s
 */

// ---------- HMR awareness (Next.js Fast Refresh) ----------
declare global {
  interface Window {
    __HMR_UPDATING?: boolean;
    __speakSeq?: any;
  }
}
const __mod: any = (globalThis as any).module;
if (typeof window !== "undefined" && __mod?.hot) {
  try {
    __mod.hot.addStatusHandler((status: string) => {
      window.__HMR_UPDATING = status !== "idle";
    });
  } catch {}
}

// ---------- Types ----------
type SpeakSequenceOption = {
  id: number;
  text: string;
  audioSrc?: string | null;
  imageSrc?: string | null;
  sequence?: number | null;
};

type Props = {
  options: SpeakSequenceOption[]; // expected length = 10 (but works with any >=1)
  question: string;
  sentence?: string;
  audioSrc?: string; // fallback reference audio
  mediaType?: string | null;
  mediaUrl?: string | null;

  // Parent Quiz (optional)
  status?: "correct" | "wrong" | "none" | "submitting";
  disabled?: boolean;

  // Called when all targets are matched or user ends session early
  onComplete?: (matchedOptionIds?: number[]) => void;

  // Tuning
  chunkMs?: number; // MediaRecorder timeslice OR WAV flush interval
  sendIntervalMs?: number; // dequeue cadence
  maxQueue?: number; // backpressure threshold
  scoreThreshold?: number; // %
  allowSkip?: boolean; // not exposed in minimal UI
};

// ---------- Arabic normalization + scoring helpers ----------
const ARABIC_DIACRITICS = /[\u064B-\u0652\u0670\u0640]/g;
const ARABIC_PUNCT_WS = /[^\u0600-\u06FF]+/g;
const MAPS: Record<string, string> = {
  أ: "ا",
  إ: "ا",
  آ: "ا",
  ٱ: "ا",
  ى: "ي",
  ؤ: "و",
  ئ: "ي",
  ة: "ه",
};

function normalizeArabic(input: string): string {
  if (!input) return "";
  let s = input.trim();
  s = s.replace(ARABIC_DIACRITICS, "");
  s = s.replace(ARABIC_PUNCT_WS, "");
  s = s
    .split("")
    .map((ch) => MAPS[ch] ?? ch)
    .join("");
  return s;
}

function levenshtein(a: string, b: string): number {
  const m = a.length,
    n = b.length;
  const dp = new Array(n + 1);
  for (let j = 0; j <= n; j++) dp[j] = j;
  for (let i = 1; i <= m; i++) {
    let prev = dp[0];
    dp[0] = i;
    for (let j = 1; j <= n; j++) {
      const tmp = dp[j];
      dp[j] =
        a[i - 1] === b[j - 1]
          ? prev
          : Math.min(prev + 1, dp[j] + 1, dp[j - 1] + 1);
      prev = tmp;
    }
  }
  return dp[n];
}

function similarityPercent(expected: string, actual: string): number {
  if (!expected && !actual) return 100;
  if (!expected || !actual) return 0;
  const dist = levenshtein(expected, actual);
  const maxLen = Math.max(expected.length, actual.length);
  return Math.max(0, Math.round(((maxLen - dist) / maxLen) * 100));
}

function isPronouncedCorrectly(
  expectedRaw: string,
  actualRaw: string,
  threshold = 80
) {
  const expected = normalizeArabic(expectedRaw);
  const actual = normalizeArabic(actualRaw);
  if (expected && actual && expected === actual)
    return { ok: true, score: 100 };
  if (
    expected &&
    actual &&
    (actual.includes(expected) || expected.includes(actual))
  ) {
    const score = Math.min(
      95,
      Math.round((actual.length / expected.length) * 100)
    );
    if (score >= threshold) return { ok: true, score };
  }
  const score = similarityPercent(expected, actual);
  return { ok: score >= threshold, score };
}

// ---------- Tiny icons (inline SVG, no external deps) ----------
const MicIcon = ({ filled = false }: { filled?: boolean }) => (
  <svg width="22" height="22" viewBox="0 0 24 24" aria-hidden="true">
    <path
      d="M12 14a4 4 0 0 0 4-4V6a4 4 0 0 0-8 0v4a4 4 0 0 0 4 4Zm7-4a1 1 0 1 0-2 0 5 5 0 0 1-10 0 1 1 0 1 0-2 0 7 7 0 0 0 6 6.92V20H8a1 1 0 1 0 0 2h8a1 1 0 1 0 0-2h-3v-3.08A7 7 0 0 0 19 10Z"
      fill={filled ? "currentColor" : "none"}
      stroke="currentColor"
      strokeWidth="1.5"
    />
  </svg>
);

const Spinner = () => (
  <svg className="animate-spin" width="20" height="20" viewBox="0 0 24 24">
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
      fill="none"
    />
    <path
      className="opacity-75"
      d="M4 12a8 8 0 0 1 8-8"
      stroke="currentColor"
      strokeWidth="4"
      strokeLinecap="round"
      fill="none"
    />
  </svg>
);

// ---------- WAV helpers (client-side) ----------
const WAV_OUT_SR = 16000;

// NEW: simple RMS to estimate energy
function rmsLevel(buf: Float32Array) {
  let sum = 0;
  for (let i = 0; i < buf.length; i++) {
    const v = buf[i];
    sum += v * v;
  }
  return Math.sqrt(sum / (buf.length || 1));
}

function downsampleBuffer(
  buffer: Float32Array,
  inRate: number,
  outRate: number
) {
  if (outRate === inRate) return buffer;
  const ratio = inRate / outRate;
  const newLen = Math.floor(buffer.length / ratio);
  const result = new Float32Array(newLen);
  let offsetResult = 0;
  let offsetBuffer = 0;
  while (offsetResult < result.length) {
    const nextOffsetBuffer = Math.round((offsetResult + 1) * ratio);
    let accum = 0,
      count = 0;
    for (let i = offsetBuffer; i < nextOffsetBuffer && i < buffer.length; i++) {
      accum += buffer[i];
      count++;
    }
    result[offsetResult] = accum / (count || 1);
    offsetResult++;
    offsetBuffer = nextOffsetBuffer;
  }
  return result;
}

function floatTo16BitPCM(float32: Float32Array) {
  const buffer = new ArrayBuffer(float32.length * 2);
  const view = new DataView(buffer);
  let offset = 0;
  for (let i = 0; i < float32.length; i++, offset += 2) {
    let s = Math.max(-1, Math.min(1, float32[i]));
    view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
  }
  return view;
}

function encodeWAVPCM16(samples: Float32Array, sampleRate: number): Blob {
  const bytesPerSample = 2;
  const blockAlign = 1 * bytesPerSample;
  const buffer = new ArrayBuffer(44 + samples.length * bytesPerSample);
  const view = new DataView(buffer);

  writeString(view, 0, "RIFF");
  view.setUint32(4, 36 + samples.length * bytesPerSample, true);
  writeString(view, 8, "WAVE");
  writeString(view, 12, "fmt ");
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, 1, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, sampleRate * blockAlign, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, 16, true);
  writeString(view, 36, "data");
  view.setUint32(40, samples.length * bytesPerSample, true);

  const pcm = floatTo16BitPCM(samples);
  const out = new Uint8Array(buffer);
  for (let i = 0; i < pcm.byteLength; i++) {
    out[44 + i] = pcm.getUint8(i);
  }

  return new Blob([buffer], { type: "audio/wav" });
}

function writeString(view: DataView, offset: number, str: string) {
  for (let i = 0; i < str.length; i++) {
    view.setUint8(offset + i, str.charCodeAt(i));
  }
}

// ---------- Component ----------
export default function SpeakSequence({
  options,
  question,
  sentence,
  audioSrc,
  mediaType,
  mediaUrl,
  status = "none",
  disabled = false,
  onComplete,
  chunkMs = 1200, // NEW default (was 900)
  sendIntervalMs = 300, // NEW default (was 220)
  maxQueue = 6,
  scoreThreshold = 80,
}: Props) {
  // ===== DEBUG CONFIG =====
  const DEBUG = true;
  const log = useCallback((...args: any[]) => {
    if (!DEBUG) return;
    console.log("[SpeakSequence]", ...args);
  }, []);
  const warn = useCallback(
    (...args: any[]) => DEBUG && console.warn("[SpeakSequence]", ...args),
    []
  );
  const errorLog = useCallback(
    (...args: any[]) => DEBUG && console.error("[SpeakSequence]", ...args),
    []
  );
  const isDev = process.env.NODE_ENV !== "production";

  // Prefer client-side WAV capture (primary); MediaRecorder fallback
  const preferClientWav = true;

  // Derived targets (cap at 10 to fit the rule)
  const targets = useMemo(() => {
    const list = (options?.length ? options : []).slice(0, 10);
    if (list.length === 0 && (sentence || question)) {
      return [{ id: -1, text: sentence || question }] as SpeakSequenceOption[];
    }
    return list;
  }, [options, question, sentence]);

  // ----------- State -----------
  const [currentIdx, setCurrentIdx] = useState(0);
  const [matchedIds, setMatchedIds] = useState<number[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [micDenied, setMicDenied] = useState(false);
  const [queueLength, setQueueLength] = useState(0);
  const [lastScore, setLastScore] = useState<number | null>(null);
  const [lastTranscript, setLastTranscript] = useState<string>("");

  // Reference audio (optional, hidden UI)
  const refAudioEl = useRef<HTMLAudioElement | null>(null);

  // ----------- Refs -----------
  const mediaRecorderRef = useRef<MediaRecorder | null>(null); // fallback path
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const queueRef = useRef<Blob[]>([]);
  const inflightRef = useRef(false);
  const isMountedRef = useRef(false);
  const senderTimerRef = useRef<number | null>(null);
  const abortRef = useRef<AbortController | null>(null);
  const completeOnceRef = useRef(false);
  const lastResponseTsRef = useRef<number>(0);

  // WAV capture refs
  const audioCtxRef = useRef<AudioContext | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const procRef = useRef<ScriptProcessorNode | null>(null);
  const wavTimerRef = useRef<number | null>(null);
  const floatChunksRef = useRef<Float32Array[]>([]);
  const captureEnabledRef = useRef<boolean>(false);
  const wavPausedRef = useRef<boolean>(false);

  // NEW: tiny VAD params/clock
  const SILENCE_RMS = 0.01; // adjust between 0.008–0.015 depending on mic/noise
  const KEEPALIVE_MS = 1200; // send a chunk at least every ~1.2s
  const lastSentAtRef = useRef<number>(0);

  // Extra debug counters + timings
  const chunksPushedRef = useRef<number>(0);
  const chunksSentRef = useRef<number>(0);
  const postCounterRef = useRef<number>(0);

  // Dev Strict Mode / HMR mount lifetime tracking
  const mountedAtRef = useRef<number>(0);

  // Re-entrancy guard for starting mic/recorder
  const startingRef = useRef<boolean>(false);

  // LIVE-STATE REFS (fix stale-closure bug)
  const isRecordingRef = useRef(false);
  const isPausedRef = useRef(false);
  const currentIdxRef = useRef(0);
  const currentTargetTextRef = useRef<string>("");

  // ----------- Helpers -----------
  const currentTarget = targets[currentIdx] || null;
  const targetKey = currentTarget?.text ?? ""; // use for stable deps
  const atEnd = currentIdx >= targets.length;
  const canStart = !isRecording && !disabled && !micDenied && !!currentTarget;

  // Expose live debug handle in DevTools
  useEffect(() => {
    window.__speakSeq = {
      mediaRecorderRef,
      mediaStreamRef,
      queueRef,
      inflightRef,
      senderTimerRef,
      abortRef,
      audioCtxRef,
      procRef,
      state: () => ({
        isRecording,
        isPaused,
        isProcessing,
        isConnecting,
        micDenied,
        queueLength,
        currentIdx,
        atEnd,
        chunksPushed: chunksPushedRef.current,
        chunksSent: chunksSentRef.current,
        hasTimer: !!senderTimerRef.current,
      }),
    };
    return () => {
      try {
        delete window.__speakSeq;
      } catch {}
    };
  }, [
    isConnecting,
    isPaused,
    isProcessing,
    isRecording,
    micDenied,
    queueLength,
    currentIdx,
    atEnd,
  ]);

  // keep live refs in sync
  useEffect(() => {
    isRecordingRef.current = isRecording;
  }, [isRecording]);
  useEffect(() => {
    isPausedRef.current = isPaused;
  }, [isPaused]);
  useEffect(() => {
    currentIdxRef.current = currentIdx;
  }, [currentIdx]);
  useEffect(() => {
    currentTargetTextRef.current = targetKey;
  }, [targetKey]);

  const stopWavCapture = useCallback(() => {
    if (wavTimerRef.current) {
      window.clearInterval(wavTimerRef.current);
      wavTimerRef.current = null;
    }
    try {
      procRef.current && procRef.current.disconnect();
      sourceRef.current && sourceRef.current.disconnect();
      audioCtxRef.current && audioCtxRef.current.close();
    } catch {}
    procRef.current = null;
    sourceRef.current = null;
    audioCtxRef.current = null;
    floatChunksRef.current = [];
    captureEnabledRef.current = false;
    wavPausedRef.current = false;
  }, []);

  const stopAll = useCallback(
    (reason?: string) => {
      try {
        mediaRecorderRef.current?.stop();
      } catch {}
      try {
        mediaStreamRef.current?.getTracks().forEach((t) => t.stop());
      } catch {}
      const mrState = mediaRecorderRef.current?.state;
      mediaRecorderRef.current = null;
      mediaStreamRef.current = null;

      stopWavCapture();

      if (senderTimerRef.current) {
        window.clearInterval(senderTimerRef.current);
        senderTimerRef.current = null;
      }
      try {
        abortRef.current?.abort();
      } catch {}
      abortRef.current = null;

      const dumped = queueRef.current.length;
      queueRef.current = [];
      setQueueLength(0);
      setIsRecording(false);
      setIsPaused(false);
      setIsProcessing(false);
      setIsConnecting(false);
      if (reason)
        log("stopAll:", {
          reason,
          dumpedQueue: dumped,
          mediaRecorderPrevState: mrState,
          chunksPushed: chunksPushedRef.current,
          chunksSent: chunksSentRef.current,
        });
    },
    [log, stopWavCapture]
  );

  const finishIfNeeded = useCallback(() => {
    if (!atEnd) return;
    if (completeOnceRef.current) return;
    completeOnceRef.current = true;
    stopAll("sequence complete");
    log("onComplete fired", { matchedIds });
    onComplete?.(matchedIds);
  }, [atEnd, matchedIds, onComplete, stopAll, log]);

  const pickMime = () => {
    if (typeof MediaRecorder !== "undefined" && MediaRecorder.isTypeSupported) {
      if (MediaRecorder.isTypeSupported("audio/webm;codecs=opus"))
        return { mimeType: "audio/webm;codecs=opus" };
      if (MediaRecorder.isTypeSupported("audio/webm"))
        return { mimeType: "audio/webm" };
      if (MediaRecorder.isTypeSupported("audio/mp4"))
        return { mimeType: "audio/mp4" };
    }
    return {};
  };

  // ===== Web Audio capture → WAV chunks =====
  const flushWavChunk = useCallback(
    (reason: string) => {
      if (!captureEnabledRef.current) return;
      const floatBlocks = floatChunksRef.current;
      if (!floatBlocks.length) return;

      // merge float blocks
      let total = 0;
      for (const b of floatBlocks) total += b.length;
      const merged = new Float32Array(total);
      let off = 0;
      for (const b of floatBlocks) {
        merged.set(b, off);
        off += b.length;
      }
      floatChunksRef.current = [];

      // NEW: Mini-VAD — drop near-silence unless keepalive interval elapsed
      const now = Date.now();
      const isKeepAlive = now - lastSentAtRef.current >= KEEPALIVE_MS;
      const level = rmsLevel(merged);
      if (level < SILENCE_RMS && !isKeepAlive) {
        // discard silent chunk
        return;
      }

      const inRate = audioCtxRef.current?.sampleRate || 48000;
      const ds = downsampleBuffer(merged, inRate, WAV_OUT_SR);
      const wavBlob = encodeWAVPCM16(ds, WAV_OUT_SR);

      queueRef.current.push(wavBlob);
      chunksPushedRef.current++;
      lastSentAtRef.current = now; // mark last sent time
      setQueueLength(queueRef.current.length);

      log("WAV flush", {
        reason,
        inRate,
        outRate: WAV_OUT_SR,
        samplesIn: merged.length,
        samplesOut: ds.length,
        bytes: wavBlob.size,
        qlen: queueRef.current.length,
        rms: Number(level.toFixed(4)),
        keepalive: isKeepAlive,
      });
    },
    [log]
  );

  const pauseWavCapture = useCallback(
    (why = "pause") => {
      wavPausedRef.current = true;
      captureEnabledRef.current = false;
      if (wavTimerRef.current) {
        window.clearInterval(wavTimerRef.current);
        wavTimerRef.current = null;
      }
      flushWavChunk(`pre-${why}`); // flush what we have (will be dropped if silent)
      log("WAV capture paused", { why });
    },
    [flushWavChunk, log]
  );

  const resumeWavCapture = useCallback(
    (why = "resume") => {
      if (!audioCtxRef.current) return;
      wavPausedRef.current = false;
      captureEnabledRef.current = true;
      if (!wavTimerRef.current) {
        wavTimerRef.current = window.setInterval(
          () => flushWavChunk("interval"),
          chunkMs
        ) as unknown as number;
      }
      log("WAV capture resumed", { why });
    },
    [chunkMs, flushWavChunk, log]
  );

  const startWavCapture = useCallback(
    (stream: MediaStream) => {
      const ctx = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      audioCtxRef.current = ctx;
      const source = ctx.createMediaStreamSource(stream);
      sourceRef.current = source;

      // ScriptProcessorNode (deprecated but widely available)
      const bufSize = 4096;
      const proc = ctx.createScriptProcessor(bufSize, 1, 1);
      procRef.current = proc;

      proc.onaudioprocess = (ev) => {
        if (!captureEnabledRef.current) return;
        const input = ev.inputBuffer.getChannelData(0);
        // copy the block because the underlying buffer is reused
        floatChunksRef.current.push(new Float32Array(input));
      };

      const gain = ctx.createGain();
      gain.gain.value = 0;
      source.connect(proc);
      proc.connect(gain);
      gain.connect(ctx.destination);

      captureEnabledRef.current = true;
      wavPausedRef.current = false;

      if (!wavTimerRef.current) {
        wavTimerRef.current = window.setInterval(
          () => flushWavChunk("interval"),
          chunkMs
        ) as unknown as number;
      }

      log("WAV capture started", { ctxRate: ctx.sampleRate, chunkMs });
    },
    [chunkMs, flushWavChunk, log]
  );

  // ----------- Sender loop (reads LIVE refs) -----------
  const startSenderLoop = useCallback(() => {
    if (senderTimerRef.current) return;
    log("startSenderLoop", { sendIntervalMs, maxQueue });

    let tickCount = 0;
    senderTimerRef.current = window.setInterval(async () => {
      tickCount++;
      if (tickCount % 50 === 0) {
        log("senderLoop: tick", {
          qlen: queueRef.current.length,
          isPaused: isPausedRef.current,
          isRecording: isRecordingRef.current,
          inflight: inflightRef.current,
        });
      }

      if (
        !isMountedRef.current ||
        isPausedRef.current ||
        !isRecordingRef.current ||
        inflightRef.current
      ) {
        return;
      }

      const q = queueRef.current;
      if (q.length === 0) return;

      setQueueLength(q.length);

      if (q.length > maxQueue) {
        // Backpressure: pause capture
        if (preferClientWav) {
          pauseWavCapture("backpressure");
        } else if (mediaRecorderRef.current) {
          try {
            mediaRecorderRef.current.pause();
          } catch {}
        }
        log("senderLoop: backpressure → pause capture", { qlen: q.length });
      }

      const blob = q.shift();
      setQueueLength(q.length);
      if (!blob) return;

      inflightRef.current = true;
      setIsProcessing(true);

      // abort any previous (older) fetch
      abortRef.current?.abort();
      abortRef.current = new AbortController();

      try {
        const postId = ++postCounterRef.current;
        chunksSentRef.current++;
        const fd = new FormData();
        const fileName = preferClientWav ? "chunk.wav" : "chunk.webm";
        fd.append("file", blob, fileName);
        fd.append("idx", String(currentIdxRef.current));
        fd.append("ts", String(Date.now()));

        log("POST /api/hf-transcribe → start", {
          postId,
          idx: currentIdxRef.current,
          qRemaining: q.length,
          blobBytes: blob.size,
        });
        console.time(`hfRoundtrip#${postId}`);

        const res = await fetch("/api/hf-transcribe", {
          method: "POST",
          body: fd,
          signal: abortRef.current.signal,
        });

        if (!res.ok) {
          const txt = await res.text();
          warn("chunk error", {
            status: res.status,
            body: txt.slice(0, 160),
            postId,
          });
          inflightRef.current = false;
          setIsProcessing(false);
          console.timeEnd(`hfRoundtrip#${postId}`);
          setError("Network or model error while analyzing. Retrying...");
          if (preferClientWav && !wavPausedRef.current)
            resumeWavCapture("post-error");
          return;
        }

        const data = (await res.json()) as {
          text?: string;
          meta?: any;
          error?: string;
        };
        console.timeEnd(`hfRoundtrip#${postId}`);

        const spoken = (data?.text || "").trim();
        setLastTranscript(spoken);

        const exp = currentTargetTextRef.current || sentence || question || "";
        const normExp = normalizeArabic(exp);
        const normSpk = normalizeArabic(spoken);
        const { ok, score } = isPronouncedCorrectly(
          exp,
          spoken,
          scoreThreshold
        );
        setLastScore(score);
        lastResponseTsRef.current = Date.now();

        log("POST /api/hf-transcribe ← done", {
          postId,
          idxNow: currentIdxRef.current,
          textPreview: spoken.slice(0, 80),
          score,
          ok,
          meta: data?.meta || "(no-meta)",
          expectedRaw: exp,
          expectedNorm: normExp,
          spokenNorm: normSpk,
        });

        if (ok) {
          queueRef.current = [];
          setQueueLength(0);

          if (preferClientWav) {
            resumeWavCapture("matched");
          } else if (mediaRecorderRef.current?.state === "paused") {
            try {
              mediaRecorderRef.current.resume();
            } catch {}
          }

          setMatchedIds((prev) => {
            const next = currentTarget?.id ? [...prev, currentTarget.id] : prev;
            log("MATCH ✅ advancing", {
              matchedId: currentTarget?.id,
              fromIdx: currentIdxRef.current,
              toIdx: currentIdxRef.current + 1,
              matchedIdsNext: next,
            });
            return next;
          });

          setCurrentIdx((idx) => idx + 1);
          currentIdxRef.current = currentIdxRef.current + 1;
          currentTargetTextRef.current =
            targets[currentIdxRef.current]?.text ?? "";
        } else {
          if (
            preferClientWav &&
            wavPausedRef.current &&
            queueRef.current.length <= Math.floor(maxQueue / 2)
          ) {
            resumeWavCapture("drain");
          } else if (
            mediaRecorderRef.current &&
            mediaRecorderRef.current.state === "paused" &&
            queueRef.current.length <= Math.floor(maxQueue / 2)
          ) {
            try {
              mediaRecorderRef.current.resume();
            } catch {}
          }
        }
      } catch (e: any) {
        if (e?.name !== "AbortError") {
          errorLog("send error:", e);
          setError(
            "Failed to send audio. Check your connection and try again."
          );
        } else {
          warn("send aborted by controller (likely new chunk superseded)");
        }
      } finally {
        inflightRef.current = false;
        setIsProcessing(false);
        if (
          preferClientWav &&
          wavPausedRef.current &&
          queueRef.current.length <= Math.floor(maxQueue / 2)
        ) {
          resumeWavCapture("finally-drain");
        } else if (
          mediaRecorderRef.current &&
          mediaRecorderRef.current.state === "paused" &&
          queueRef.current.length <= Math.floor(maxQueue / 2)
        ) {
          try {
            mediaRecorderRef.current.resume();
          } catch {}
        }
      }
    }, sendIntervalMs) as unknown as number;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    sendIntervalMs,
    maxQueue,
    question,
    scoreThreshold,
    log,
    warn,
    errorLog,
    resumeWavCapture,
    pauseWavCapture,
    preferClientWav,
  ]);

  const handleStart = useCallback(async () => {
    if (!currentTarget) return;
    if (startingRef.current) {
      log("handleStart ignored (already starting)");
      return;
    }
    startingRef.current = true;
    setError(null);
    setIsConnecting(true);
    setIsPaused(false);

    try {
      log("getUserMedia: requesting mic…");
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });
      mediaStreamRef.current = stream;

      const track = stream.getAudioTracks()?.[0];
      log("mic OK", {
        label: track?.label || "(no-label)",
        settings: track?.getSettings?.() || "(no-settings)",
        constraints: track?.getConstraints?.() || "(no-constraints)",
      });

      if (preferClientWav) {
        // Primary path: Web Audio → WAV chunks
        startWavCapture(stream);
        setIsRecording(true);
        isRecordingRef.current = true;
        setIsConnecting(false);
        setIsProcessing(false);
        currentIdxRef.current = currentIdx;
        currentTargetTextRef.current = currentTarget?.text ?? "";
        startSenderLoop();
      } else {
        // Fallback: MediaRecorder (webm/opus)
        const rec = new MediaRecorder(stream, pickMime());
        mediaRecorderRef.current = rec;

        rec.ondataavailable = (ev: BlobEvent) => {
          if (ev.data && ev.data.size > 0) {
            queueRef.current.push(ev.data);
            chunksPushedRef.current++;
            setQueueLength(queueRef.current.length);
            if (!senderTimerRef.current) {
              log("ondataavailable → senderLoop missing, re-starting");
              startSenderLoop();
            }
            if (chunksPushedRef.current % 5 === 0) {
              log("ondataavailable", {
                pushed: chunksPushedRef.current,
                lastChunkBytes: ev.data.size,
                qlen: queueRef.current.length,
              });
            }
          }
        };

        rec.onstart = () => {
          log("MediaRecorder.onstart", { mimeType: rec.mimeType, chunkMs });
          setIsRecording(true);
          isRecordingRef.current = true;
          setIsConnecting(false);
          setIsProcessing(false);
          currentIdxRef.current = currentIdx;
          currentTargetTextRef.current = currentTarget?.text ?? "";
          startSenderLoop();
        };

        rec.onpause = () => log("MediaRecorder.onpause");
        rec.onresume = () => log("MediaRecorder.onresume");
        rec.onstop = () =>
          log("MediaRecorder.onstop", {
            chunksPushed: chunksPushedRef.current,
            chunksSent: chunksSentRef.current,
          });
        rec.onerror = (e) => errorLog("MediaRecorder.onerror", e);

        rec.start(chunkMs);
      }
    } catch (e) {
      errorLog("mic error:", e);
      setMicDenied(true);
      setIsConnecting(false);
      setError(
        "Microphone permission denied or unsupported. Enable mic access or try another browser."
      );
    } finally {
      startingRef.current = false;
    }
  }, [
    chunkMs,
    currentIdx,
    currentTarget,
    startSenderLoop,
    log,
    errorLog,
    preferClientWav,
    startWavCapture,
  ]);

  const handleToggleMic = useCallback(() => {
    if (disabled || atEnd) return;
    if (!isRecording) {
      log("toggle → start");
      void handleStart();
      return;
    }
    if (isPaused) {
      try {
        if (preferClientWav) {
          resumeWavCapture("toggle");
          setIsPaused(false);
          isPausedRef.current = false;
          log("toggle → resume (WAV)");
        } else {
          mediaRecorderRef.current?.resume();
          setIsPaused(false);
          isPausedRef.current = false;
          log("toggle → resume");
        }
      } catch {}
    } else {
      try {
        if (preferClientWav) {
          pauseWavCapture("toggle");
          setIsPaused(true);
          isPausedRef.current = true;
          log("toggle → pause (WAV)");
        } else {
          try {
            mediaRecorderRef.current?.requestData?.();
          } catch {}
          mediaRecorderRef.current?.pause();
          setIsPaused(true);
          isPausedRef.current = true;
          log("toggle → pause");
        }
      } catch {}
    }
  }, [
    atEnd,
    disabled,
    handleStart,
    isPaused,
    isRecording,
    log,
    preferClientWav,
    pauseWavCapture,
    resumeWavCapture,
  ]);

  const handleStop = useCallback(() => {
    log("End session clicked");
    stopAll("user stop");
    onComplete?.(matchedIds);
  }, [matchedIds, onComplete, stopAll, log]);

  // ----------- Lifecycle with Strict-Mode + HMR Unmount Guard -----------
  useEffect(() => {
    isMountedRef.current = true;
    mountedAtRef.current = Date.now();
    log("mount", {
      targetsLen: targets.length,
      firstTarget: targets[0]?.text,
      props: { chunkMs, sendIntervalMs, maxQueue, scoreThreshold },
    });

    return () => {
      isMountedRef.current = false;
      const lifeMs = Date.now() - mountedAtRef.current;
      const devStrictDouble = isDev && lifeMs < 1500;
      const hmr = !!window.__HMR_UPDATING;

      if (devStrictDouble || hmr) {
        log("unmount (dev/hmr) — skipping stopAll", { lifeMs, hmr });
        return;
      }
      stopAll("unmount");
      log("unmount");
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [targets.length, chunkMs, sendIntervalMs, maxQueue, scoreThreshold]);

  // Ensure sender loop exists whenever recording is true (recover after HMR)
  useEffect(() => {
    if (isRecording && !senderTimerRef.current) {
      log("ensure senderLoop present (post-mount/state-change)");
      startSenderLoop();
    }
  }, [isRecording, startSenderLoop, log]);

  // ---------- ONLY abort on true target/index change ----------
  const prevIdxRef = useRef<number>(currentIdx);
  const prevTextRef = useRef<string>(targetKey);

  useEffect(() => {
    const idxChanged = prevIdxRef.current !== currentIdx;
    const textChanged = prevTextRef.current !== targetKey;

    // UI resets on each (benign)
    setLastScore(null);
    setLastTranscript("");
    setError(null);

    if (idxChanged || textChanged) {
      if (abortRef.current) {
        try {
          abortRef.current.abort();
          log("abort in-flight due to target advance", {
            fromIdx: prevIdxRef.current,
            toIdx: currentIdx,
            fromText: prevTextRef.current,
            toText: targetKey,
          });
        } catch {}
      }
      prevIdxRef.current = currentIdx;
      prevTextRef.current = targetKey;
      log("target changed", { currentIdx, newTarget: targetKey, matchedIds });
    } else {
      log("target index changed (no-op)", {
        currentIdx,
        newTarget: targetKey,
        matchedIds,
      });
    }
  }, [currentIdx, targetKey, matchedIds, log]);

  useEffect(() => {
    log("state change", {
      isRecording,
      isPaused,
      isProcessing,
      isConnecting,
      micDenied,
      queueLength,
      currentIdx,
      atEnd,
      hasTimer: !!senderTimerRef.current,
    });
  }, [
    isRecording,
    isPaused,
    isProcessing,
    isConnecting,
    micDenied,
    queueLength,
    currentIdx,
    atEnd,
    log,
  ]);

  useEffect(() => {
    if (atEnd) finishIfNeeded();
  }, [atEnd, finishIfNeeded]);

  const analyzing = useMemo(() => {
    if (!isProcessing) return false;
    const last = lastResponseTsRef.current || 0;
    return Date.now() - last > 150;
  }, [isProcessing]);

  // ---------- Minimal UI (screenshot style) ----------
  return (
    <div
      className="w-full rounded-xl border border-neutral-200 bg-white shadow-sm"
      aria-live="polite"
    >
      {/* Card body */}
      <div className="p-6">
        {/* Visual area */}
        <div className="h-[360px] md:h-[420px] rounded-xl border border-neutral-200 bg-white flex items-center justify-center">
          {/* Big centered target */}
          <div
            className="text-[64px] md:text-[96px] font-extrabold text-neutral-900 select-none leading-none"
            title="Current target"
          >
            {currentTarget?.text || sentence || question || "—"}
          </div>
        </div>

        {/* Bottom mic bar */}
        <div className="mt-6">
          <div className="w-full">
            <button
              onClick={handleToggleMic}
              disabled={
                disabled || micDenied || (!isRecording && !canStart) || atEnd
              }
              className="w-full h-14 md:h-16 rounded-xl bg-neutral-50 hover:bg-neutral-100 disabled:opacity-50 flex items-center justify-center gap-3 text-neutral-900 border border-neutral-200 transition"
              aria-label={
                atEnd
                  ? "Sequence completed"
                  : !isRecording
                  ? "Start recording"
                  : isPaused
                  ? "Resume recording"
                  : "Pause recording"
              }
            >
              {/* State indicator on the left (spinner while analyzing) */}
              <span className="w-5 h-5 flex items-center justify-center text-neutral-700">
                {isConnecting || analyzing ? <Spinner /> : null}
              </span>

              {/* Mic icon */}
              <span
                className={`relative inline-flex items-center justify-center ${
                  isRecording && !isPaused
                    ? "text-neutral-900"
                    : "text-neutral-600"
                }`}
              >
                {isRecording && !isPaused ? (
                  <>
                    <span className="absolute inline-flex h-8 w-8 rounded-full opacity-20 bg-neutral-900 animate-ping" />
                    <MicIcon filled />
                  </>
                ) : (
                  <MicIcon />
                )}
              </span>

              {/* Label */}
              <span className="text-sm md:text-base font-medium">
                {atEnd
                  ? "Completed"
                  : !isRecording
                  ? "Tap to start"
                  : isPaused
                  ? "Tap to resume"
                  : "Tap to pause"}
              </span>
            </button>
          </div>

          {/* Small status line under the bar (kept subtle) */}
          <div className="mt-2 text-center text-xs md:text-sm text-neutral-500">
            {micDenied
              ? "Microphone permission denied."
              : atEnd
              ? "All items matched."
              : isConnecting
              ? "Connecting to microphone…"
              : isRecording && isPaused
              ? "Paused"
              : isRecording
              ? analyzing
                ? "Analyzing…"
                : "Listening…"
              : "Ready"}
          </div>
        </div>

        {/* Hidden but useful: error + last transcript (very subtle) */}
        {error && (
          <div className="mt-4 rounded-lg border border-rose-200 bg-rose-50 p-3 text-rose-800 text-sm">
            {error}
          </div>
        )}
        {lastTranscript && (
          <div className="mt-3 text-center text-[12px] text-neutral-400 truncate">
            “{lastTranscript}” {lastScore != null ? `• ${lastScore}%` : ""}
          </div>
        )}

        {/* Optional tiny foot actions (not in screenshot, kept minimal) */}
        <div className="mt-4 flex items-center justify-center gap-4">
          <button
            type="button"
            onClick={handleStop}
            className="text-xs text-neutral-500 hover:text-neutral-700 underline"
          >
            End session
          </button>
          <span className="text-neutral-300">•</span>
          <span className="text-xs text-neutral-400">
            {currentIdx}/{targets.length}
          </span>
          <span className="text-neutral-300">•</span>
          <span className="text-xs text-neutral-400">Queue {queueLength}</span>
        </div>
      </div>
    </div>
  );
}
